{"_from": "is-lambda@^1.0.1", "_id": "is-lambda@1.0.1", "_inBundle": false, "_integrity": "sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ==", "_location": "/is-lambda", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-lambda@^1.0.1", "name": "is-lambda", "escapedName": "is-lambda", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/make-fetch-happen"], "_resolved": "https://registry.npmjs.org/is-lambda/-/is-lambda-1.0.1.tgz", "_shasum": "3d9877899e6a53efc0160504cde15f82e6f061d5", "_spec": "is-lambda@^1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/make-fetch-happen", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son"}, "bugs": {"url": "https://github.com/watson/is-lambda/issues"}, "bundleDependencies": false, "coordinates": [37.3859955, -122.0838831], "dependencies": {}, "deprecated": false, "description": "Detect if your code is running on an AWS Lambda server", "devDependencies": {"clear-require": "^1.0.1", "standard": "^10.0.2"}, "homepage": "https://github.com/watson/is-lambda", "keywords": ["aws", "hosting", "hosted", "lambda", "detect"], "license": "MIT", "main": "index.js", "name": "is-lambda", "repository": {"type": "git", "url": "git+https://github.com/watson/is-lambda.git"}, "scripts": {"test": "standard && node test.js"}, "version": "1.0.1"}