{"_from": "path-key@^2.0.0", "_id": "path-key@2.0.1", "_inBundle": false, "_integrity": "sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==", "_location": "/npm-run-path/path-key", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "path-key@^2.0.0", "name": "path-key", "escapedName": "path-key", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/npm-run-path"], "_resolved": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "_shasum": "411cadb574c5a140d3a4b1910d40d80cc9f40b40", "_spec": "path-key@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/npm-run-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/path-key/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Get the PATH environment variable key cross-platform", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/path-key#readme", "keywords": ["path", "key", "environment", "env", "variable", "var", "get", "cross-platform", "windows"], "license": "MIT", "name": "path-key", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-key.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.1", "xo": {"esnext": true}}