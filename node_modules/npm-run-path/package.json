{"_from": "npm-run-path@^2.0.0", "_id": "npm-run-path@2.0.2", "_inBundle": false, "_integrity": "sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw==", "_location": "/npm-run-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "npm-run-path@^2.0.0", "name": "npm-run-path", "escapedName": "npm-run-path", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/execa"], "_resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz", "_shasum": "35a9232dfa35d7067b4cb2ddf2357b1871536c5f", "_spec": "npm-run-path@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/execa", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "bundleDependencies": false, "dependencies": {"path-key": "^2.0.0"}, "deprecated": false, "description": "Get your PATH prepended with locally installed binaries", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "license": "MIT", "name": "npm-run-path", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/npm-run-path.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.2", "xo": {"esnext": true}}