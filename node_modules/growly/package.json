{"_from": "growly@^1.3.0", "_id": "growly@1.3.0", "_inBundle": false, "_integrity": "sha512-+xGQY0YyAWCnqy7Cd++hc2JqMYzlm0dG30Jd0beaA64sROr8C4nt8Yc9V5Ro3avlSUDTN0ulqP/VBKi1/lLygw==", "_location": "/growly", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "growly@^1.3.0", "name": "growly", "escapedName": "growly", "rawSpec": "^1.3.0", "saveSpec": null, "fetchSpec": "^1.3.0"}, "_requiredBy": ["/node-notifier"], "_resolved": "https://registry.npmjs.org/growly/-/growly-1.3.0.tgz", "_shasum": "f10748cbe76af964b7c96c93c6bcc28af120c081", "_spec": "growly@^1.3.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/node-notifier", "author": {"name": "<PERSON>", "email": "abra<PERSON><PERSON><PERSON><PERSON>@gmail.com", "url": "http://ibrahimalrajhi.com/"}, "bugs": {"url": "http://github.com/theabraham/growly/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Simple zero-dependency Growl notifications using GNTP.", "directories": {"example": "example", "lib": "lib"}, "homepage": "https://github.com/theabraham/growly#readme", "keywords": ["growl", "growly", "snarl", "notifications", "gntp", "messages"], "license": "MIT", "main": "lib/growly.js", "name": "growly", "repository": {"type": "git", "url": "git+ssh://**************/theabraham/growly.git"}, "version": "1.3.0"}