{"_args": [["nopt@5.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "nopt@5.0.0", "_id": "nopt@5.0.0", "_inBundle": false, "_integrity": "sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==", "_location": "/nopt", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "nopt@5.0.0", "name": "nopt", "escapedName": "nopt", "rawSpec": "5.0.0", "saveSpec": null, "fetchSpec": "5.0.0"}, "_requiredBy": ["/node-gyp"], "_resolved": "https://registry.npmjs.org/nopt/-/nopt-5.0.0.tgz", "_spec": "5.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bin": {"nopt": "bin/nopt.js"}, "bugs": {"url": "https://github.com/npm/nopt/issues"}, "dependencies": {"abbrev": "1"}, "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "devDependencies": {"tap": "^14.10.6"}, "engines": {"node": ">=6"}, "files": ["bin", "lib"], "homepage": "https://github.com/npm/nopt#readme", "license": "ISC", "main": "lib/nopt.js", "name": "nopt", "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "scripts": {"postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "test": "tap test/*.js"}, "version": "5.0.0"}