{"_args": [["no-case@2.3.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "no-case@2.3.2", "_id": "no-case@2.3.2", "_inBundle": false, "_integrity": "sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ==", "_location": "/no-case", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "no-case@2.3.2", "name": "no-case", "escapedName": "no-case", "rawSpec": "2.3.2", "saveSpec": null, "fetchSpec": "2.3.2"}, "_requiredBy": ["/camel-case", "/param-case"], "_resolved": "https://registry.npmjs.org/no-case/-/no-case-2.3.2.tgz", "_spec": "2.3.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "bugs": {"url": "https://github.com/blakeembrey/no-case/issues"}, "dependencies": {"lower-case": "^1.1.1"}, "description": "Remove case from a string", "devDependencies": {"chai": "^4.0.2", "istanbul": "^0.4.3", "jsesc": "^2.2.0", "mocha": "^3.0.0", "standard": "^10.0.2", "xregexp": "^3.1.1"}, "files": ["no-case.js", "no-case.d.ts", "vendor", "LICENSE"], "homepage": "https://github.com/blakeembrey/no-case", "keywords": ["no", "case", "space", "lower", "trim"], "license": "MIT", "main": "no-case.js", "name": "no-case", "repository": {"type": "git", "url": "git://github.com/blakeembrey/no-case.git"}, "scripts": {"build": "node build.js", "lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail"}, "standard": {"ignore": ["coverage/**"]}, "typings": "no-case.d.ts", "version": "2.3.2"}