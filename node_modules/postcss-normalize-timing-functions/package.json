{"_args": [["postcss-normalize-timing-functions@4.0.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "postcss-normalize-timing-functions@4.0.2", "_id": "postcss-normalize-timing-functions@4.0.2", "_inBundle": false, "_integrity": "sha512-acwJY95edP762e++00Ehq9L4sZCEcOPyaHwoaFOhIwWCDfik6YvqsYNxckee65JHLKzuNSSmAdxwD2Cud1Z54A==", "_location": "/postcss-normalize-timing-functions", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "postcss-normalize-timing-functions@4.0.2", "name": "postcss-normalize-timing-functions", "escapedName": "postcss-normalize-timing-functions", "rawSpec": "4.0.2", "saveSpec": null, "fetchSpec": "4.0.2"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "https://registry.npmjs.org/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-4.0.2.tgz", "_spec": "4.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "dependencies": {"cssnano-util-get-match": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "description": "Normalize CSS animation/transition timing functions.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["LICENSE-MIT", "dist"], "homepage": "https://github.com/cssnano/cssnano", "license": "MIT", "main": "dist/index.js", "name": "postcss-normalize-timing-functions", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.2"}