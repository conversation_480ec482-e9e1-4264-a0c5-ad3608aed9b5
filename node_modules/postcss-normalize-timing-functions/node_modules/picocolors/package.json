{"_args": [["picocolors@0.2.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "picocolors@0.2.1", "_id": "picocolors@0.2.1", "_inBundle": false, "_integrity": "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==", "_location": "/postcss-normalize-timing-functions/picocolors", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "picocolors@0.2.1", "name": "picocolors", "escapedName": "picocolors", "rawSpec": "0.2.1", "saveSpec": null, "fetchSpec": "0.2.1"}, "_requiredBy": ["/postcss-normalize-timing-functions/postcss"], "_resolved": "https://registry.npmjs.org/picocolors/-/picocolors-0.2.1.tgz", "_spec": "0.2.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>"}, "browser": {"./picocolors.js": "./picocolors.browser.js"}, "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "description": "The tiniest and the fastest coloring library ever", "files": ["picocolors.*", "types.ts"], "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "license": "ISC", "main": "./picocolors.js", "name": "picocolors", "repository": {"type": "git", "url": "git+https://github.com/alexeyraspopov/picocolors.git"}, "sideEffects": false, "types": "./picocolors.d.ts", "version": "0.2.1"}