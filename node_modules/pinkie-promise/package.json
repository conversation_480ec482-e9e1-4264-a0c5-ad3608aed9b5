{"_from": "pinkie-promise@^2.0.0", "_id": "pinkie-promise@2.0.1", "_inBundle": false, "_integrity": "sha512-0Gni6D4UcLTbv9c57DfxDGdr41XfgUjqWZu492f0cIGr16zDU06BWP/RAEvOuo7CQ0CNjHaLlM59YJJFm3NWlw==", "_location": "/pinkie-promise", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pinkie-promise@^2.0.0", "name": "pinkie-promise", "escapedName": "pinkie-promise", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/del/globby", "/internal-ip/find-up", "/internal-ip/load-json-file", "/internal-ip/path-exists", "/internal-ip/path-type", "/webpack-dev-server/find-up", "/webpack-dev-server/load-json-file", "/webpack-dev-server/path-exists", "/webpack-dev-server/path-type"], "_resolved": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "_shasum": "2135d6dfa7a358c069ac9b178776288228450ffa", "_spec": "pinkie-promise@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/del/node_modules/globby", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "bugs": {"url": "https://github.com/floatdrop/pinkie-promise/issues"}, "bundleDependencies": false, "dependencies": {"pinkie": "^2.0.0"}, "deprecated": false, "description": "ES2015 Promise ponyfill", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/floatdrop/pinkie-promise#readme", "keywords": ["promise", "promises", "es2015", "es6", "polyfill", "ponyfill"], "license": "MIT", "name": "pinkie-promise", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/pinkie-promise.git"}, "scripts": {"test": "mocha"}, "version": "2.0.1"}