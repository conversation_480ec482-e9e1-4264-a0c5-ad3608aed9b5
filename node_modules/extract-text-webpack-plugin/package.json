{"_from": "extract-text-webpack-plugin@^3.0.0", "_id": "extract-text-webpack-plugin@3.0.2", "_inBundle": false, "_integrity": "sha512-bt/LZ4m5Rqt/Crl2HiKuAl/oqg0psx1tsTLkvWbJen1CtD+fftkZhMaQ9HOtY2gWsl2Wq+sABmMVi9z3DhKWQQ==", "_location": "/extract-text-webpack-plugin", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "extract-text-webpack-plugin@^3.0.0", "name": "extract-text-webpack-plugin", "escapedName": "extract-text-webpack-plugin", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/extract-text-webpack-plugin/-/extract-text-webpack-plugin-3.0.2.tgz", "_shasum": "5f043eaa02f9750a9258b78c0a6e0dc1408fb2f7", "_spec": "extract-text-webpack-plugin@^3.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack-contrib/extract-text-webpack-plugin/issues"}, "bundleDependencies": false, "dependencies": {"async": "^2.4.1", "loader-utils": "^1.1.0", "schema-utils": "^0.3.0", "webpack-sources": "^1.0.1"}, "deprecated": "Deprecated. Please use https://github.com/webpack-contrib/mini-css-extract-plugin", "description": "Extract text from bundle into a file.", "devDependencies": {"babel-cli": "^6.26.0", "babel-jest": "^21.2.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.1", "cross-env": "^5.1.0", "css-loader": "^0.28.7", "del-cli": "^1.1.0", "eslint": "^4.9.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-import": "^2.8.0", "file-loader": "^1.1.5", "jest": "^21.2.1", "lint-staged": "^4.3.0", "nsp": "^2.8.1", "pre-commit": "^1.2.2", "raw-loader": "^0.5.1", "standard-version": "^4.2.0", "style-loader": "^0.19.0", "webpack": "^3.8.1", "webpack-defaults": "^1.6.0"}, "engines": {"node": ">= 4.8 < 5.0.0 || >= 5.10"}, "files": ["dist", "schema"], "homepage": "http://github.com/webpack-contrib/extract-text-webpack-plugin", "license": "MIT", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "main": "dist/cjs.js", "name": "extract-text-webpack-plugin", "peerDependencies": {"webpack": "^3.1.0"}, "pre-commit": "lint-staged", "repository": {"type": "git", "url": "git+ssh://**************/webpack-contrib/extract-text-webpack-plugin.git"}, "scripts": {"appveyor:test": "npm run test", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js'", "build:example": "(cd example && webpack)", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepublish": "npm run build", "release": "standard-version", "security": "nsp check", "start": "npm run build -- -w", "test": "jest", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "test:watch": "jest --watch", "travis:coverage": "npm run test:coverage -- --runInBand", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "webpack-defaults": "webpack-defaults"}, "version": "3.0.2"}