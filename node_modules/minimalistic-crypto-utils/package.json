{"_from": "minimalistic-crypto-utils@^1.0.1", "_id": "minimalistic-crypto-utils@1.0.1", "_inBundle": false, "_integrity": "sha512-JIYlbt6g8i5jKfJ3xz7rF0LXmv2TkDxBLUkiBeZ7bAx4GnnNMr8xFpGnOxn6GhTEHx3SjRrZEoU+j04prX1ktg==", "_location": "/minimalistic-crypto-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "minimalistic-crypto-utils@^1.0.1", "name": "minimalistic-crypto-utils", "escapedName": "minimalistic-crypto-utils", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/elliptic", "/hmac-drbg"], "_resolved": "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "_shasum": "f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a", "_spec": "minimalistic-crypto-utils@^1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/elliptic", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/indutny/minimalistic-crypto-utils/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Minimalistic tools for JS crypto modules", "devDependencies": {"mocha": "^3.2.0"}, "homepage": "https://github.com/indutny/minimalistic-crypto-utils#readme", "keywords": ["minimalistic", "utils", "crypto"], "license": "MIT", "main": "lib/utils.js", "name": "minimalistic-crypto-utils", "repository": {"type": "git", "url": "git+ssh://**************/indutny/minimalistic-crypto-utils.git"}, "scripts": {"test": "mocha --reporter=spec test/*-test.js"}, "version": "1.0.1"}