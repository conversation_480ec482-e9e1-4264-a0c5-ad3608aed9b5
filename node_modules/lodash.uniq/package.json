{"_from": "lodash.uniq@^4.5.0", "_id": "lodash.uniq@4.5.0", "_inBundle": false, "_integrity": "sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==", "_location": "/lodash.uniq", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lodash.uniq@^4.5.0", "name": "lodash.uniq", "escapedName": "lodash.uniq", "rawSpec": "^4.5.0", "saveSpec": null, "fetchSpec": "^4.5.0"}, "_requiredBy": ["/caniuse-api", "/cssnano-preset-default/caniuse-api"], "_resolved": "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz", "_shasum": "d0225373aeb652adc1bc82e4945339a842754773", "_spec": "lodash.uniq@^4.5.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/caniuse-api", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "deprecated": false, "description": "The lodash method `_.uniq` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "keywords": ["lodash-modularized", "uniq"], "license": "MIT", "name": "lodash.uniq", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "version": "4.5.0"}