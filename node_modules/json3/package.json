{"_args": [["json3@3.3.3", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "json3@3.3.3", "_id": "json3@3.3.3", "_inBundle": false, "_integrity": "sha512-c7/8mbUsKigAbLkD5B010BK4D9LZm7A1pNItkEwiUZRpIN66exu/e7YQWysGun+TRKaJp8MhemM+VkfWv42aCA==", "_location": "/json3", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "json3@3.3.3", "name": "json3", "escapedName": "json3", "rawSpec": "3.3.3", "saveSpec": null, "fetchSpec": "3.3.3"}, "_requiredBy": ["/sockjs-client"], "_resolved": "https://registry.npmjs.org/json3/-/json3-3.3.3.tgz", "_spec": "3.3.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, "bugs": {"url": "https://github.com/bestiejs/json3/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tech.roxee.tv/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://fb.me/ok"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://oxy.fi/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/rma4ok"}], "description": "A JSON polyfill for older JavaScript platforms.", "devDependencies": {"curl-amd": "~0.8.12", "highlight.js": "~8.3.0", "marked": "~0.3.2", "requirejs": "~2.1.15", "spec": "~1.0.1", "tar": "~1.0.2"}, "files": ["README.md", "LICENSE", "lib/json3.js", "lib/json3.min.js"], "homepage": "https://bestiejs.github.io/json3", "jam": {"main": "./lib/json3.js", "includes": ["README.md", "LICENSE", "lib/json3.js", "lib/json3.min.js"]}, "keywords": ["json", "spec", "ecma", "es5", "lexer", "parser", "stringify"], "license": "MIT", "main": "./lib/json3", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}], "name": "json3", "repository": {"type": "git", "url": "git://github.com/bestiejs/json3.git"}, "scripts": {"test": "node test/test_*.js"}, "version": "3.3.3", "volo": {"type": "directory", "ignore": [".*", "build.js", "index.html", "component.json", "bower.json", "benchmark", "page", "test", "vendor"]}}