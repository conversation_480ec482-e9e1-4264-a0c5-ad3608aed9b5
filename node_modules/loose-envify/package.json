{"_args": [["loose-envify@1.3.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "loose-envify@1.3.1", "_id": "loose-envify@1.3.1", "_inBundle": false, "_integrity": "sha1-0aitM/qc4OcT1l/dCsi3SNR4yEg=", "_location": "/loose-envify", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "loose-envify@1.3.1", "name": "loose-envify", "escapedName": "loose-envify", "rawSpec": "1.3.1", "saveSpec": null, "fetchSpec": "1.3.1"}, "_requiredBy": ["/invariant"], "_resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.3.1.tgz", "_spec": "1.3.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"loose-envify": "cli.js"}, "bugs": {"url": "https://github.com/zertosh/loose-envify/issues"}, "dependencies": {"js-tokens": "^3.0.0"}, "description": "Fast (and loose) selective `process.env` replacer using js-tokens instead of an AST", "devDependencies": {"browserify": "^13.1.1", "envify": "^3.4.0", "tap": "^8.0.0"}, "homepage": "https://github.com/zertosh/loose-envify", "keywords": ["environment", "variables", "browserify", "browserify-transform", "transform", "source", "configuration"], "license": "MIT", "main": "index.js", "name": "loose-envify", "repository": {"type": "git", "url": "git://github.com/zertosh/loose-envify.git"}, "scripts": {"test": "tap test/*.js"}, "version": "1.3.1"}