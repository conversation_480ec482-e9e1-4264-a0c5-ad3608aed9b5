{"_args": [["restore-cursor@2.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "restore-cursor@2.0.0", "_id": "restore-cursor@2.0.0", "_inBundle": false, "_integrity": "sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q==", "_location": "/restore-cursor", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "restore-cursor@2.0.0", "name": "restore-cursor", "escapedName": "restore-cursor", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/cli-cursor"], "_resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz", "_spec": "2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/restore-cursor/issues"}, "dependencies": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}, "description": "Gracefully restore the CLI cursor on exit", "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/restore-cursor#readme", "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "license": "MIT", "name": "restore-cursor", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/restore-cursor.git"}, "version": "2.0.0"}