{"_from": "normalize.css@^8.0.0", "_id": "normalize.css@8.0.1", "_inBundle": false, "_integrity": "sha512-qizSNPO93t1YUuUhP22btGOo3chcvDFqFaj2TRybP0DMxkHOCTYwp3n34fel4a31ORXy4m1Xq0Gyqpb5m33qIg==", "_location": "/normalize.css", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "normalize.css@^8.0.0", "name": "normalize.css", "escapedName": "normalize.css", "rawSpec": "^8.0.0", "saveSpec": null, "fetchSpec": "^8.0.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/normalize.css/-/normalize.css-8.0.1.tgz", "_shasum": "9b98a208738b9cc2634caacbc42d131c97487bf3", "_spec": "normalize.css@^8.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "bugs": {"url": "https://github.com/necolas/normalize.css/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A modern alternative to CSS resets", "files": ["LICENSE.md", "normalize.css"], "homepage": "https://necolas.github.io/normalize.css", "license": "MIT", "main": "normalize.css", "name": "normalize.css", "repository": {"type": "git", "url": "git+https://github.com/necolas/normalize.css.git"}, "style": "normalize.css", "version": "8.0.1"}