{"_from": "graceful-fs@^4.2.6", "_id": "graceful-fs@4.2.11", "_inBundle": false, "_integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "_location": "/graceful-fs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "graceful-fs@^4.2.6", "name": "graceful-fs", "escapedName": "graceful-fs", "rawSpec": "^4.2.6", "saveSpec": null, "fetchSpec": "^4.2.6"}, "_requiredBy": ["/copy-webpack-plugin/cacache", "/enhanced-resolve", "/fs-write-stream-atomic", "/internal-ip/load-json-file", "/internal-ip/path-type", "/load-json-file", "/node-gyp", "/uglifyjs-webpack-plugin/cacache", "/watchpack", "/watchpack-chokidar2/readdirp", "/webpack-dev-server/load-json-file", "/webpack-dev-server/path-type", "/webpack-dev-server/readdirp"], "_resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "_shasum": "4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3", "_spec": "graceful-fs@^4.2.6", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/node-gyp", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A drop-in replacement for fs, making various improvements.", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^16.3.4"}, "directories": {"test": "test"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js", "clone.js"], "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "main": "graceful-fs.js", "name": "graceful-fs", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "scripts": {"postpublish": "git push origin --follow-tags", "posttest": "nyc report", "postversion": "npm publish", "preversion": "npm test", "test": "nyc --silent node test.js | tap -c -"}, "tap": {"reporter": "classic"}, "version": "4.2.11"}