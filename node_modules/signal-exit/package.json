{"_args": [["signal-exit@3.0.7", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "signal-exit@3.0.7", "_id": "signal-exit@3.0.7", "_inBundle": false, "_integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "_location": "/signal-exit", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "signal-exit@3.0.7", "name": "signal-exit", "escapedName": "signal-exit", "rawSpec": "3.0.7", "saveSpec": null, "fetchSpec": "3.0.7"}, "_requiredBy": ["/execa", "/gauge", "/loud-rejection", "/npmlog/gauge", "/restore-cursor"], "_resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "_spec": "3.0.7", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tapjs/signal-exit/issues"}, "description": "when you want to fire an event no matter how a process exits.", "devDependencies": {"chai": "^3.5.0", "coveralls": "^3.1.1", "nyc": "^15.1.0", "standard-version": "^9.3.1", "tap": "^15.1.1"}, "files": ["index.js", "signals.js"], "homepage": "https://github.com/tapjs/signal-exit", "keywords": ["signal", "exit"], "license": "ISC", "main": "index.js", "name": "signal-exit", "repository": {"type": "git", "url": "git+https://github.com/tapjs/signal-exit.git"}, "scripts": {"postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "snap": "tap", "test": "tap"}, "version": "3.0.7"}