{"_args": [["internal-slot@1.1.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "internal-slot@1.1.0", "_id": "internal-slot@1.1.0", "_inBundle": false, "_integrity": "sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==", "_location": "/internal-slot", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "internal-slot@1.1.0", "name": "internal-slot", "escapedName": "internal-slot", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/es-abstract", "/stop-iteration-iterator"], "_resolved": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.1.0.tgz", "_spec": "1.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "description": "ES spec-like internal slots", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "homepage": "https://github.com/ljharb/internal-slot#readme", "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "license": "MIT", "main": "index.js", "name": "internal-slot", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/internal-slot.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.1.0"}