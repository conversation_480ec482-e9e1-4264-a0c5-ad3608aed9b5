{"_from": "http-deceiver@^1.2.7", "_id": "http-deceiver@1.2.7", "_inBundle": false, "_integrity": "sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw==", "_location": "/http-deceiver", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "http-deceiver@^1.2.7", "name": "http-deceiver", "escapedName": "http-deceiver", "rawSpec": "^1.2.7", "saveSpec": null, "fetchSpec": "^1.2.7"}, "_requiredBy": ["/spdy"], "_resolved": "https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz", "_shasum": "fa7168944ab9a519d337cb0bec7284dc3e723d87", "_spec": "http-deceiver@^1.2.7", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/spdy", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/indutny/http-deceiver/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Deceive HTTP parser", "devDependencies": {"handle-thing": "^1.0.1", "mocha": "^2.2.5", "readable-stream": "^2.0.1", "stream-pair": "^1.0.0"}, "homepage": "https://github.com/indutny/http-deceiver#readme", "keywords": ["http", "net", "deceive"], "license": "MIT", "main": "lib/deceiver.js", "name": "http-deceiver", "repository": {"type": "git", "url": "git+ssh://**************/indutny/http-deceiver.git"}, "scripts": {"test": "mocha --reporter=spec test/*-test.js"}, "version": "1.2.7"}