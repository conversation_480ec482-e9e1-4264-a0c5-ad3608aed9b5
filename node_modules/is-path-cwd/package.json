{"_from": "is-path-cwd@^1.0.0", "_id": "is-path-cwd@1.0.0", "_inBundle": false, "_integrity": "sha512-cnS56eR9SPAscL77ik76ATVqoPARTqPIVkMDVxRaWH06zT+6+CzIroYRJ0VVvm0Z1zfAvxvz9i/D3Ppjaqt5Nw==", "_location": "/is-path-cwd", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-path-cwd@^1.0.0", "name": "is-path-cwd", "escapedName": "is-path-cwd", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/del"], "_resolved": "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-1.0.0.tgz", "_shasum": "d225ec23132e89edd38fda767472e62e65f1106d", "_spec": "is-path-cwd@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/del", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-path-cwd/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if a path is CWD", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/is-path-cwd#readme", "keywords": ["path", "cwd", "pwd", "check", "filepath", "file", "folder"], "license": "MIT", "name": "is-path-cwd", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-path-cwd.git"}, "scripts": {"test": "mocha"}, "version": "1.0.0"}