{"_from": "ignore@^3.3.5", "_id": "ignore@3.3.10", "_inBundle": false, "_integrity": "sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug==", "_location": "/ignore", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ignore@^3.3.5", "name": "ignore", "escapedName": "ignore", "rawSpec": "^3.3.5", "saveSpec": null, "fetchSpec": "^3.3.5"}, "_requiredBy": ["/globby"], "_resolved": "https://registry.npmjs.org/ignore/-/ignore-3.3.10.tgz", "_shasum": "0a97fb876986e8081c631160f8f9f389157f0043", "_spec": "ignore@^3.3.5", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/globby", "author": {"name": "kael"}, "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "bundleDependencies": false, "deprecated": false, "description": "<PERSON><PERSON><PERSON> is a manager and filter for .gitignore rules.", "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-es2015": "^6.24.1", "chai": "~1.7.2", "codecov": "^3.0.2", "istanbul": "^0.4.5", "mkdirp": "^0.5.1", "mocha": "~1.13.0", "pre-suf": "^1.0.4", "rimraf": "^2.6.2", "spawn-sync": "^1.0.15", "tmp": "0.0.33", "typescript": "^2.9.2"}, "files": ["ignore.js", "index.d.ts"], "homepage": "https://github.com/kaelzhang/node-ignore#readme", "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "license": "MIT", "main": "./ignore.js", "name": "ignore", "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "scripts": {"build": "babel -o ignore.js index.js", "cov-report": "istanbul report", "prepublish": "npm run build", "test": "npm run tsc && npm run build && istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec ./test/ignore.js && codecov", "test-no-cov": "npm run tsc && npm run build && mocha --reporter spec ./test/ignore.js", "tsc": "tsc ./test/ts/simple.ts"}, "version": "3.3.10"}