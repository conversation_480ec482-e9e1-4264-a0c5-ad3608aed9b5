{"_from": "p-limit@^2.2.0", "_id": "p-limit@2.3.0", "_inBundle": false, "_integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "_location": "/p-limit", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "p-limit@^2.2.0", "name": "p-limit", "escapedName": "p-limit", "rawSpec": "^2.2.0", "saveSpec": null, "fetchSpec": "^2.2.0"}, "_requiredBy": ["/p-locate"], "_resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "_shasum": "3dd33c647a214fdfffd835933eb086da0dc21db1", "_spec": "p-limit@^2.2.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/p-locate", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "bundleDependencies": false, "dependencies": {"p-try": "^2.0.0"}, "deprecated": false, "description": "Run multiple promise-returning & async functions with limited concurrency", "devDependencies": {"ava": "^1.2.1", "delay": "^4.1.0", "in-range": "^1.0.0", "random-int": "^1.0.0", "time-span": "^2.0.0", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/p-limit#readme", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "license": "MIT", "name": "p-limit", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-limit.git"}, "scripts": {"test": "xo && ava && tsd-check"}, "version": "2.3.0"}