{"_args": [["type-fest@0.6.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "type-fest@0.6.0", "_id": "type-fest@0.6.0", "_inBundle": false, "_integrity": "sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==", "_location": "/read-pkg/type-fest", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "type-fest@0.6.0", "name": "type-fest", "escapedName": "type-fest", "rawSpec": "0.6.0", "saveSpec": null, "fetchSpec": "0.6.0"}, "_requiredBy": ["/read-pkg"], "_resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.6.0.tgz", "_spec": "0.6.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/type-fest/issues"}, "description": "A collection of essential TypeScript types", "devDependencies": {"@sindresorhus/tsconfig": "^0.4.0", "@typescript-eslint/eslint-plugin": "^1.9.0", "@typescript-eslint/parser": "^1.10.2", "eslint-config-xo-typescript": "^0.14.0", "tsd": "^0.7.3", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.d.ts", "source"], "homepage": "https://github.com/sindresorhus/type-fest#readme", "keywords": ["typescript", "ts", "types", "utility", "util", "utilities", "omit", "merge", "json"], "license": "(MIT OR CC0-1.0)", "name": "type-fest", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/type-fest.git"}, "scripts": {"test": "xo && tsd"}, "version": "0.6.0", "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"import/no-unresolved": "off", "@typescript-eslint/indent": "off"}}}