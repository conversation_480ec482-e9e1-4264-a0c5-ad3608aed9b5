{"_from": "debug@4", "_id": "debug@4.4.1", "_inBundle": false, "_integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "_location": "/http-proxy-agent/debug", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "debug@4", "name": "debug", "escapedName": "debug", "rawSpec": "4", "saveSpec": null, "fetchSpec": "4"}, "_requiredBy": ["/http-proxy-agent"], "_resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "_shasum": "e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b", "_spec": "debug@4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/http-proxy-agent", "author": {"name": "<PERSON>", "url": "https://github.com/qix-"}, "browser": "./src/browser.js", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"ms": "^2.1.3"}, "deprecated": false, "description": "Lightweight debugging utility for Node.js and the browser", "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "sinon": "^14.0.0", "xo": "^0.23.0"}, "engines": {"node": ">=6.0"}, "files": ["src", "LICENSE", "README.md"], "homepage": "https://github.com/debug-js/debug#readme", "keywords": ["debug", "log", "debugger"], "license": "MIT", "main": "./src/index.js", "name": "debug", "peerDependenciesMeta": {"supports-color": {"optional": true}}, "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls", "test:node": "mocha test.js test.node.js"}, "version": "4.4.1", "xo": {"rules": {"import/extensions": "off"}}}