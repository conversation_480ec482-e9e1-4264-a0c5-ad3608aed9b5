{"_from": "http-proxy-agent@^4.0.1", "_id": "http-proxy-agent@4.0.1", "_inBundle": false, "_integrity": "sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==", "_location": "/http-proxy-agent", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "http-proxy-agent@^4.0.1", "name": "http-proxy-agent", "escapedName": "http-proxy-agent", "rawSpec": "^4.0.1", "saveSpec": null, "fetchSpec": "^4.0.1"}, "_requiredBy": ["/make-fetch-happen"], "_resolved": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz", "_shasum": "8a8c8ef7f5932ccf953c296ca8291b95aa74aa3a", "_spec": "http-proxy-agent@^4.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/make-fetch-happen", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "bundleDependencies": false, "dependencies": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "deprecated": false, "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "devDependencies": {"@types/debug": "4", "@types/node": "^12.12.11", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "proxy": "1", "rimraf": "^3.0.0", "typescript": "^3.5.3"}, "engines": {"node": ">= 6"}, "files": ["dist"], "homepage": "https://github.com/TooTallNate/node-http-proxy-agent#readme", "keywords": ["http", "proxy", "endpoint", "agent"], "license": "MIT", "main": "./dist/index.js", "name": "http-proxy-agent", "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "scripts": {"build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run build", "test": "mocha", "test-lint": "eslint src --ext .js,.ts"}, "types": "./dist/index.d.ts", "version": "4.0.1"}