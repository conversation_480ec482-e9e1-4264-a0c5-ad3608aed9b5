{"_from": "os-browserify@^0.3.0", "_id": "os-browserify@0.3.0", "_inBundle": false, "_integrity": "sha512-gjcpUc3clBf9+210TRaDWbf+rZZZEshZ+DlXMRCeAjp0xhTrnQsKHypIy1J3d5hKdUzj69t708EHtU8P6bUn0A==", "_location": "/os-browserify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "os-browserify@^0.3.0", "name": "os-browserify", "escapedName": "os-browserify", "rawSpec": "^0.3.0", "saveSpec": null, "fetchSpec": "^0.3.0"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://registry.npmjs.org/os-browserify/-/os-browserify-0.3.0.tgz", "_shasum": "854373c7f5c2315914fc9bfc6bd8238fdda1ec27", "_spec": "os-browserify@^0.3.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/node-libs-browser", "author": {"name": "<PERSON>r<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "browser": "browser.js", "bugs": {"url": "https://github.com/CoderPuppy/os-browserify/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The [os](https://nodejs.org/api/os.html) module from node.js, but for browsers.", "homepage": "https://github.com/CoderPuppy/os-browserify#readme", "jspm": {"map": {"./main.js": {"node": "@node/os", "browser": "./browser.js"}}}, "license": "MIT", "main": "main.js", "name": "os-browserify", "repository": {"type": "git", "url": "git+ssh://**************/CoderPuppy/os-browserify.git"}, "version": "0.3.0"}