{"_args": [["colormin@1.1.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "colormin@1.1.2", "_id": "colormin@1.1.2", "_inBundle": false, "_integrity": "sha512-XSEQUUQUR/lXqGyddiNH3XYFUPYlYr1vXy9rTFMsSOw+J7Q6EQkdlQIrTlYn4TccpsOaUE1PYQNjBn20gwCdgQ==", "_location": "/colormin", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "colormin@1.1.2", "name": "colormin", "escapedName": "colormin", "rawSpec": "1.1.2", "saveSpec": null, "fetchSpec": "1.1.2"}, "_requiredBy": ["/postcss-colormin"], "_resolved": "https://registry.npmjs.org/colormin/-/colormin-1.1.2.tgz", "_spec": "1.1.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "ava": {"require": "babel-register"}, "bugs": {"url": "https://github.com/ben-eb/colormin/issues"}, "dependencies": {"color": "^0.11.0", "css-color-names": "0.0.4", "has": "^1.0.1"}, "description": "Turn a CSS color into its smallest representation.", "devDependencies": {"ava": "^0.16.0", "babel-cli": "^6.3.17", "babel-core": "^6.3.26", "babel-plugin-add-module-exports": "^0.2.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2015-loose": "^7.0.0", "babel-preset-stage-0": "^6.3.13", "babel-register": "^6.9.0", "del-cli": "^0.2.0", "eslint": "^3.0.0", "eslint-config-cssnano": "^3.0.0", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-import": "^1.10.2"}, "eslintConfig": {"extends": "cssnano"}, "files": ["LICENSE-MIT", "dist"], "homepage": "https://github.com/ben-eb/colormin", "keywords": ["color", "colors", "compression", "css", "minify"], "license": "MIT", "main": "dist/index.js", "name": "colormin", "repository": {"type": "git", "url": "git+https://github.com/ben-eb/colormin.git"}, "scripts": {"prepublish": "del-cli dist && babel src --out-dir dist --ignore /__tests__/", "pretest": "eslint src", "test": "ava src/__tests__", "test-012": "ava src/__tests__"}, "version": "1.1.2"}