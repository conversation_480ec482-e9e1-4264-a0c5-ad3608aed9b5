{"_from": "multicast-dns-service-types@^1.1.0", "_id": "multicast-dns-service-types@1.1.0", "_inBundle": false, "_integrity": "sha512-cnAsSVxIDsYt0v7HmC0hWZFwwXSh+E6PgCrREDuN/EsjgLwA5XRmlMHhSiDPrt6HxY1gTivEa/Zh7GtODoLevQ==", "_location": "/multicast-dns-service-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "multicast-dns-service-types@^1.1.0", "name": "multicast-dns-service-types", "escapedName": "multicast-dns-service-types", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/bonjour"], "_resolved": "https://registry.npmjs.org/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz", "_shasum": "899f11d9686e5e05cb91b35d5f0e63b773cfc901", "_spec": "multicast-dns-service-types@^1.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/bonjour", "author": {"name": "<PERSON>", "url": "@mafintosh"}, "bugs": {"url": "https://github.com/mafintosh/multicast-dns-service-types/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Parse and stringify mdns service types", "devDependencies": {"standard": "^3.5.0", "tape": "^4.0.0"}, "homepage": "https://github.com/mafintosh/multicast-dns-service-types", "keywords": ["mdns", "bonjour", "zero", "conf"], "license": "MIT", "main": "index.js", "name": "multicast-dns-service-types", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/multicast-dns-service-types.git"}, "scripts": {"test": "standard && tape test.js"}, "version": "1.1.0"}