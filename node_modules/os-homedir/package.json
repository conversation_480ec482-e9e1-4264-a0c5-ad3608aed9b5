{"_args": [["os-homedir@1.0.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "os-homedir@1.0.2", "_id": "os-homedir@1.0.2", "_inBundle": false, "_integrity": "sha1-/7xJiDNuDoM94MFox+8VISGqf7M=", "_location": "/os-homedir", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "os-homedir@1.0.2", "name": "os-homedir", "escapedName": "os-homedir", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/home-or-tmp"], "_resolved": "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz", "_spec": "1.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/os-homedir/issues"}, "description": "Node.js 4 `os.homedir()` ponyfill", "devDependencies": {"ava": "*", "path-exists": "^2.0.0", "xo": "^0.16.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/os-homedir#readme", "keywords": ["builtin", "core", "ponyfill", "polyfill", "shim", "os", "homedir", "home", "dir", "directory", "folder", "user", "path"], "license": "MIT", "name": "os-homedir", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/os-homedir.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.2"}