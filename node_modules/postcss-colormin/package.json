{"_args": [["postcss-colormin@2.2.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "postcss-colormin@2.2.2", "_id": "postcss-colormin@2.2.2", "_inBundle": false, "_integrity": "sha512-XXitQe+jNNPf+vxvQXIQ1+pvdQKWKgkx8zlJNltcMEmLma1ypDRDQwlLt+6cP26fBreihNhZxohh1rcgCH2W5w==", "_location": "/postcss-colormin", "_phantomChildren": {"escape-string-regexp": "1.0.5", "has-ansi": "2.0.0", "js-base64": "2.6.4", "strip-ansi": "3.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "postcss-colormin@2.2.2", "name": "postcss-colormin", "escapedName": "postcss-colormin", "rawSpec": "2.2.2", "saveSpec": null, "fetchSpec": "2.2.2"}, "_requiredBy": ["/cssnano"], "_resolved": "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-2.2.2.tgz", "_spec": "2.2.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "ava": {"require": "babel-register"}, "bugs": {"url": "https://github.com/ben-eb/postcss-colormin/issues"}, "dependencies": {"colormin": "^1.0.5", "postcss": "^5.0.13", "postcss-value-parser": "^3.2.3"}, "description": "Minify colors in your CSS files with PostCSS.", "devDependencies": {"ava": "^0.17.0", "babel-cli": "^6.3.17", "babel-core": "^6.3.26", "babel-plugin-add-module-exports": "^0.2.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2015-loose": "^7.0.0", "babel-preset-stage-0": "^6.3.13", "babel-register": "^6.9.0", "del-cli": "^0.2.0", "eslint": "^3.0.0", "eslint-config-cssnano": "^3.0.0", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-import": "^2.0.1"}, "eslintConfig": {"extends": "cssnano"}, "files": ["dist", "LICENSE-MIT"], "homepage": "https://github.com/ben-eb/postcss-colormin", "keywords": ["color", "colors", "compression", "css", "minify", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-colormin", "repository": {"type": "git", "url": "git+https://github.com/ben-eb/postcss-colormin.git"}, "scripts": {"prepublish": "del-cli dist && babel src --out-dir dist --ignore /__tests__/", "pretest": "eslint src", "test": "ava src/__tests__", "test-012": "ava src/__tests__"}, "version": "2.2.2"}