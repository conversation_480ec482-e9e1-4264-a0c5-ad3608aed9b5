{"_from": "is-stream@^1.1.0", "_id": "is-stream@1.1.0", "_inBundle": false, "_integrity": "sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==", "_location": "/is-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-stream@^1.1.0", "name": "is-stream", "escapedName": "is-stream", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/execa"], "_resolved": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "_shasum": "12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44", "_spec": "is-stream@^1.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/execa", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-stream/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if something is a Node.js stream", "devDependencies": {"ava": "*", "tempfile": "^1.1.0", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/is-stream#readme", "keywords": ["stream", "type", "streams", "writable", "readable", "duplex", "transform", "check", "detect", "is"], "license": "MIT", "name": "is-stream", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-stream.git"}, "scripts": {"test": "xo && ava"}, "version": "1.1.0"}