{"_args": [["loud-rejection@1.6.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "loud-rejection@1.6.0", "_id": "loud-rejection@1.6.0", "_inBundle": false, "_integrity": "sha512-RPNliZOFkqFumDhvYqOaNY4Uz9oJM2K9tC6JWsJJsNdhuONW4LQHRBpb0qf4pJApVffI5N39SwzWZJuEhfd7eQ==", "_location": "/loud-rejection", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "loud-rejection@1.6.0", "name": "loud-rejection", "escapedName": "loud-rejection", "rawSpec": "1.6.0", "saveSpec": null, "fetchSpec": "1.6.0"}, "_requiredBy": ["/internal-ip/meow"], "_resolved": "https://registry.npmjs.org/loud-rejection/-/loud-rejection-1.6.0.tgz", "_spec": "1.6.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/loud-rejection/issues"}, "dependencies": {"currently-unhandled": "^0.4.1", "signal-exit": "^3.0.0"}, "description": "Make unhandled promise rejections fail loudly instead of the default silent fail", "devDependencies": {"ava": "*", "bluebird": "^3.0.5", "coveralls": "^2.11.4", "delay": "^1.0.0", "execa": "^0.4.0", "get-stream": "^2.0.0", "nyc": "^6.2.1", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "register.js", "api.js"], "homepage": "https://github.com/sindresorhus/loud-rejection#readme", "keywords": ["promise", "promises", "unhandled", "uncaught", "rejection", "loud", "fail", "catch", "throw", "handler", "exit", "debug", "debugging", "verbose"], "license": "MIT", "name": "loud-rejection", "nyc": {"exclude": ["fixture.js"]}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/loud-rejection.git"}, "scripts": {"coveralls": "nyc report --reporter=text-lcov | coveralls", "test": "xo && nyc ava"}, "version": "1.6.0"}