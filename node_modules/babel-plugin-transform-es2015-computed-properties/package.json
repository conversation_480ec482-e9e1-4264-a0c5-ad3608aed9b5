{"_args": [["babel-plugin-transform-es2015-computed-properties@6.24.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "babel-plugin-transform-es2015-computed-properties@6.24.1", "_id": "babel-plugin-transform-es2015-computed-properties@6.24.1", "_inBundle": false, "_integrity": "sha512-C/uAv4ktFP/Hmh01gMTvYvICrKze0XVX9f2PdIXuriCSvUmV9j+u+BB9f5fJK3+878yMK6dkdcq+Ymr9mrcLzw==", "_location": "/babel-plugin-transform-es2015-computed-properties", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-plugin-transform-es2015-computed-properties@6.24.1", "name": "babel-plugin-transform-es2015-computed-properties", "escapedName": "babel-plugin-transform-es2015-computed-properties", "rawSpec": "6.24.1", "saveSpec": null, "fetchSpec": "6.24.1"}, "_requiredBy": ["/babel-preset-env"], "_resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-computed-properties/-/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz", "_spec": "6.24.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "dependencies": {"babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}, "description": "Compile ES2015 computed properties to ES5", "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "babel-plugin-transform-es2015-computed-properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-es2015-computed-properties"}, "version": "6.24.1"}