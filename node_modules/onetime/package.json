{"_from": "onetime@^2.0.0", "_id": "onetime@2.0.1", "_inBundle": false, "_integrity": "sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ==", "_location": "/onetime", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "onetime@^2.0.0", "name": "onetime", "escapedName": "onetime", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/restore-cursor"], "_resolved": "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz", "_shasum": "067428230fd67443b2794b22bba528b6867962d4", "_spec": "onetime@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/restore-cursor", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "bundleDependencies": false, "dependencies": {"mimic-fn": "^1.0.0"}, "deprecated": false, "description": "Ensure a function is only called once", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/onetime#readme", "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "license": "MIT", "name": "onetime", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.1"}