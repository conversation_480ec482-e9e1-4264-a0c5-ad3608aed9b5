{"_args": [["cosmiconfig@5.2.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "cosmiconfig@5.2.1", "_id": "cosmiconfig@5.2.1", "_inBundle": false, "_integrity": "sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA==", "_location": "/cosmiconfig", "_phantomChildren": {"argparse": "1.0.10", "error-ex": "1.3.2", "json-parse-better-errors": "1.0.2"}, "_requested": {"type": "version", "registry": true, "raw": "cosmiconfig@5.2.1", "name": "cosmiconfig", "escapedName": "cosmiconfig", "rawSpec": "5.2.1", "saveSpec": null, "fetchSpec": "5.2.1"}, "_requiredBy": ["/optimize-css-assets-webpack-plugin/cssnano", "/postcss-load-config"], "_resolved": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz", "_spec": "5.2.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "babel": {"plugins": ["transform-flow-strip-types"]}, "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.1", "parse-json": "^4.0.0"}, "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "del": "^3.0.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "make-dir": "^1.2.0", "parent-module": "^0.1.0", "prettier": "^1.8.2", "remark-cli": "^5.0.0", "remark-preset-davidtheclark": "^0.7.0"}, "engines": {"node": ">=4"}, "files": ["dist"], "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true}, "keywords": ["load", "configuration", "config"], "license": "MIT", "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.md": ["npm run lint:md-partial", "git add"]}, "main": "dist/index.js", "name": "cosmiconfig", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "scripts": {"build": "flow-remove-types src --out-dir dist --quiet", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "format": "prettier --write \"{src/*.js,test/*.js}\"", "lint": "eslint . && npm run lint:md", "lint:fix": "eslint . --fix", "lint:md": "npm run lint:md-partial -- *.md", "lint:md-partial": "remark -u remark-preset-david<PERSON><PERSON><PERSON>k --frail --quiet --no-stdout --output --", "precommit": "lint-staged && jest && flow check", "prepublishOnly": "npm run build", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch"}, "version": "5.2.1"}