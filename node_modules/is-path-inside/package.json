{"_from": "is-path-inside@^1.0.0", "_id": "is-path-inside@1.0.1", "_inBundle": false, "_integrity": "sha512-qhsCR/Esx4U4hg/9I19OVUAJkGWtjRYHMRgUMZE2TDdj+Ag+kttZanLupfddNyglzz50cUlmWzUaI37GDfNx/g==", "_location": "/is-path-inside", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-path-inside@^1.0.0", "name": "is-path-inside", "escapedName": "is-path-inside", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/is-path-in-cwd"], "_resolved": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-1.0.1.tgz", "_shasum": "8ef5b7de50437a3fdca6b4e865ef7aa55cb48036", "_spec": "is-path-inside@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/is-path-in-cwd", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-path-inside/issues"}, "bundleDependencies": false, "dependencies": {"path-is-inside": "^1.0.1"}, "deprecated": false, "description": "Check if a path is inside another path", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/is-path-inside#readme", "keywords": ["path", "inside", "folder", "directory", "dir", "file", "resolve"], "license": "MIT", "name": "is-path-inside", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-path-inside.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.1"}