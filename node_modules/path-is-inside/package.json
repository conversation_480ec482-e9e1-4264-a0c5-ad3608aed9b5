{"_from": "path-is-inside@^1.0.1", "_id": "path-is-inside@1.0.2", "_inBundle": false, "_integrity": "sha512-DUWJr3+ULp4zXmol/SZkFf3JGsS9/SIv+Y3Rt93/UjPpDpklB5f1er4O3POIbUuUJ3FXgqte2Q7SrU6zAqwk8w==", "_location": "/path-is-inside", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "path-is-inside@^1.0.1", "name": "path-is-inside", "escapedName": "path-is-inside", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/is-path-inside"], "_resolved": "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz", "_shasum": "365417dede44430d1c11af61027facf074bdfc53", "_spec": "path-is-inside@^1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/is-path-inside", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me"}, "bugs": {"url": "https://github.com/domenic/path-is-inside/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Tests whether one path is inside another path", "devDependencies": {"jshint": "~2.3.0", "mocha": "~1.15.1"}, "files": ["lib"], "homepage": "https://github.com/domenic/path-is-inside#readme", "keywords": ["path", "directory", "folder", "inside", "relative"], "license": "(WTFPL OR MIT)", "main": "lib/path-is-inside.js", "name": "path-is-inside", "repository": {"type": "git", "url": "git+https://github.com/domenic/path-is-inside.git"}, "scripts": {"lint": "jshint lib", "test": "mocha"}, "version": "1.0.2"}