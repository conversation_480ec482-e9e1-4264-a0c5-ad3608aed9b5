{"_args": [["node-notifier@5.4.5", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "node-notifier@5.4.5", "_id": "node-notifier@5.4.5", "_inBundle": false, "_integrity": "sha512-tVbHs7DyTLtzOiN78izLA85zRqB9NvEXkAf014Vx3jtSvn/xBl6bR8ZYifj+dFcFrKI21huSQgJZ6ZtL3B4HfQ==", "_location": "/node-notifier", "_phantomChildren": {"isexe": "2.0.0"}, "_requested": {"type": "version", "registry": true, "raw": "node-notifier@5.4.5", "name": "node-notifier", "escapedName": "node-notifier", "rawSpec": "5.4.5", "saveSpec": null, "fetchSpec": "5.4.5"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/node-notifier/-/node-notifier-5.4.5.tgz", "_spec": "5.4.5", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^1.1.0", "semver": "^5.5.0", "shellwords": "^0.1.1", "which": "^1.3.0"}, "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "devDependencies": {"eslint": "^5.12.1", "eslint-config-semistandard": "^13.0.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.15.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "husky": "^1.3.1", "jest": "^23.2.0", "lint-staged": "^8.1.0", "prettier": "^1.12.1"}, "directories": {"example": "example", "test": "test"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupTestFrameworkScriptFile": "./test/_test-matchers.js"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "license": "MIT", "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "main": "index.js", "name": "node-notifier", "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "scripts": {"example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js", "precommit": "lint-staged", "pretest": "npm run lint", "test": "jest"}, "version": "5.4.5"}