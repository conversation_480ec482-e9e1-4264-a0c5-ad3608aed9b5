{"_from": "which@^1.3.0", "_id": "which@1.3.1", "_inBundle": false, "_integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "_location": "/node-notifier/which", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "which@^1.3.0", "name": "which", "escapedName": "which", "rawSpec": "^1.3.0", "saveSpec": null, "fetchSpec": "^1.3.0"}, "_requiredBy": ["/node-notifier"], "_resolved": "https://registry.npmjs.org/which/-/which-1.3.1.tgz", "_shasum": "a45043d54f5805316da8d62f9f50918d3da70b0a", "_spec": "which@^1.3.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/node-notifier", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "bin": {"which": "bin/which"}, "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bundleDependencies": false, "dependencies": {"isexe": "^2.0.0"}, "deprecated": false, "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.6.2", "tap": "^12.0.1"}, "files": ["which.js", "bin/which"], "homepage": "https://github.com/isaacs/node-which#readme", "license": "ISC", "main": "which.js", "name": "which", "repository": {"type": "git", "url": "git://github.com/isaacs/node-which.git"}, "scripts": {"changelog": "bash gen-changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}", "test": "tap test/*.js --cov"}, "version": "1.3.1"}