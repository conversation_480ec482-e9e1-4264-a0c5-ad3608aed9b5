{"_args": [["is-weakset@2.0.4", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "is-weakset@2.0.4", "_id": "is-weakset@2.0.4", "_inBundle": false, "_integrity": "sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==", "_location": "/is-weakset", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "is-weakset@2.0.4", "name": "is-weakset", "escapedName": "is-weakset", "rawSpec": "2.0.4", "saveSpec": null, "fetchSpec": "2.0.4"}, "_requiredBy": ["/which-collection"], "_resolved": "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.4.tgz", "_spec": "2.0.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-weakset/issues"}, "dependencies": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "description": "Is this value a JS WeakSet? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/get-intrinsic": "^1.2.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "core-js": "^2.6.12", "encoding": "^0.1.13", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-weakset#readme", "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "license": "MIT", "main": "index.js", "name": "is-weakset", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakset.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "tests-only": "nyc tape 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "tests:shims": "nyc tape --require=es5-shim --require=es6-shim 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "2.0.4"}