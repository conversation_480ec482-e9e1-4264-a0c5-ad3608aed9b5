{"_from": "has-proto@^1.2.0", "_id": "has-proto@1.2.0", "_inBundle": false, "_integrity": "sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==", "_location": "/has-proto", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "has-proto@^1.2.0", "name": "has-proto", "escapedName": "has-proto", "rawSpec": "^1.2.0", "saveSpec": null, "fetchSpec": "^1.2.0"}, "_requiredBy": ["/es-abstract", "/typed-array-byte-length", "/typed-array-byte-offset"], "_resolved": "https://registry.npmjs.org/has-proto/-/has-proto-1.2.0.tgz", "_shasum": "5de5a6eabd95fdffd9818b43055e8065e39fe9d5", "_spec": "has-proto@^1.2.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/has-proto/issues"}, "bundleDependencies": false, "dependencies": {"dunder-proto": "^1.0.0"}, "deprecated": false, "description": "Does this environment have the ability to get the [[Prototype]] of an object on creation with `__proto__`?", "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/gopd": "^1.0.3", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "gopd": "^1.2.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "reflect.getprototypeof": "^1.0.7", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./accessor": "./accessor.js", "./mutator": "./mutator.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/has-proto#readme", "keywords": ["prototype", "proto", "set", "get", "__proto__", "getPrototypeOf", "setPrototypeOf", "has"], "license": "MIT", "main": "index.js", "name": "has-proto", "publishConfig": {"ignore": [".github/workflows", "types"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-proto.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js"}, "version": "1.2.0"}