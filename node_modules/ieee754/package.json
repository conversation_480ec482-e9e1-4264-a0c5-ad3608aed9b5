{"_from": "ieee754@^1.1.4", "_id": "ieee754@1.2.1", "_inBundle": false, "_integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "_location": "/ieee754", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ieee754@^1.1.4", "name": "ieee754", "escapedName": "ieee754", "rawSpec": "^1.1.4", "saveSpec": null, "fetchSpec": "^1.1.4"}, "_requiredBy": ["/buffer"], "_resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "_shasum": "8eb7a10a63fff25d15a57b001586d177d1b0d352", "_spec": "ieee754@^1.1.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/buffer", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "devDependencies": {"airtap": "^3.0.0", "standard": "*", "tape": "^5.0.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "homepage": "https://github.com/feross/ieee754#readme", "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "name": "ieee754", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "types": "index.d.ts", "version": "1.2.1"}