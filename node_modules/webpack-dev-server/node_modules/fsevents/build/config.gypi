# Do not edit. File was generated by node-gyp's "configure" step
{
  "target_defaults": {
    "cflags": [],
    "default_configuration": "Release",
    "defines": [],
    "include_dirs": [],
    "libraries": []
  },
  "variables": {
    "asan": 0,
    "build_v8_with_gn": "false",
    "coverage": "false",
    "dcheck_always_on": 0,
    "debug_nghttp2": "false",
    "debug_node": "false",
    "enable_lto": "false",
    "enable_pgo_generate": "false",
    "enable_pgo_use": "false",
    "error_on_warn": "false",
    "force_dynamic_crt": 0,
    "host_arch": "x64",
    "icu_data_in": "../../deps/icu-tmp/icudt70l.dat",
    "icu_endianness": "l",
    "icu_gyp_path": "tools/icu/icu-generic.gyp",
    "icu_path": "deps/icu-small",
    "icu_small": "false",
    "icu_ver_major": "70",
    "is_debug": 0,
    "llvm_version": "11.0",
    "napi_build_version": "8",
    "node_byteorder": "little",
    "node_debug_lib": "false",
    "node_enable_d8": "false",
    "node_install_corepack": "true",
    "node_install_npm": "true",
    "node_library_files": [
      "lib/constants.js",
      "lib/net.js",
      "lib/trace_events.js",
      "lib/events.js",
      "lib/repl.js",
      "lib/util.js",
      "lib/dgram.js",
      "lib/vm.js",
      "lib/stream.js",
      "lib/child_process.js",
      "lib/assert.js",
      "lib/_tls_wrap.js",
      "lib/http2.js",
      "lib/inspector.js",
      "lib/os.js",
      "lib/_http_server.js",
      "lib/console.js",
      "lib/perf_hooks.js",
      "lib/readline.js",
      "lib/punycode.js",
      "lib/_http_incoming.js",
      "lib/https.js",
      "lib/_stream_wrap.js",
      "lib/domain.js",
      "lib/dns.js",
      "lib/_http_client.js",
      "lib/diagnostics_channel.js",
      "lib/tty.js",
      "lib/_http_agent.js",
      "lib/timers.js",
      "lib/_http_outgoing.js",
      "lib/querystring.js",
      "lib/_tls_common.js",
      "lib/module.js",
      "lib/_stream_passthrough.js",
      "lib/_stream_transform.js",
      "lib/worker_threads.js",
      "lib/sys.js",
      "lib/_stream_duplex.js",
      "lib/path.js",
      "lib/_http_common.js",
      "lib/string_decoder.js",
      "lib/cluster.js",
      "lib/v8.js",
      "lib/crypto.js",
      "lib/wasi.js",
      "lib/_stream_readable.js",
      "lib/zlib.js",
      "lib/url.js",
      "lib/tls.js",
      "lib/_stream_writable.js",
      "lib/async_hooks.js",
      "lib/process.js",
      "lib/http.js",
      "lib/buffer.js",
      "lib/fs.js",
      "lib/internal/constants.js",
      "lib/internal/abort_controller.js",
      "lib/internal/net.js",
      "lib/internal/v8_prof_processor.js",
      "lib/internal/event_target.js",
      "lib/internal/inspector_async_hook.js",
      "lib/internal/validators.js",
      "lib/internal/linkedlist.js",
      "lib/internal/cli_table.js",
      "lib/internal/repl.js",
      "lib/internal/util.js",
      "lib/internal/histogram.js",
      "lib/internal/error_serdes.js",
      "lib/internal/dgram.js",
      "lib/internal/child_process.js",
      "lib/internal/assert.js",
      "lib/internal/fixed_queue.js",
      "lib/internal/blocklist.js",
      "lib/internal/v8_prof_polyfill.js",
      "lib/internal/options.js",
      "lib/internal/worker.js",
      "lib/internal/dtrace.js",
      "lib/internal/idna.js",
      "lib/internal/watchdog.js",
      "lib/internal/encoding.js",
      "lib/internal/tty.js",
      "lib/internal/freeze_intrinsics.js",
      "lib/internal/timers.js",
      "lib/internal/heap_utils.js",
      "lib/internal/querystring.js",
      "lib/internal/js_stream_socket.js",
      "lib/internal/errors.js",
      "lib/internal/priority_queue.js",
      "lib/internal/freelist.js",
      "lib/internal/blob.js",
      "lib/internal/socket_list.js",
      "lib/internal/socketaddress.js",
      "lib/internal/stream_base_commons.js",
      "lib/internal/url.js",
      "lib/internal/tls.js",
      "lib/internal/async_hooks.js",
      "lib/internal/http.js",
      "lib/internal/buffer.js",
      "lib/internal/trace_events_async_hooks.js",
      "lib/internal/crypto/sig.js",
      "lib/internal/crypto/util.js",
      "lib/internal/crypto/scrypt.js",
      "lib/internal/crypto/random.js",
      "lib/internal/crypto/keys.js",
      "lib/internal/crypto/certificate.js",
      "lib/internal/crypto/keygen.js",
      "lib/internal/crypto/diffiehellman.js",
      "lib/internal/crypto/cipher.js",
      "lib/internal/crypto/hash.js",
      "lib/internal/crypto/pbkdf2.js",
      "lib/internal/cluster/shared_handle.js",
      "lib/internal/cluster/round_robin_handle.js",
      "lib/internal/cluster/worker.js",
      "lib/internal/cluster/master.js",
      "lib/internal/cluster/utils.js",
      "lib/internal/cluster/child.js",
      "lib/internal/bootstrap/loaders.js",
      "lib/internal/bootstrap/pre_execution.js",
      "lib/internal/bootstrap/node.js",
      "lib/internal/bootstrap/environment.js",
      "lib/internal/bootstrap/switches/does_not_own_process_state.js",
      "lib/internal/bootstrap/switches/is_not_main_thread.js",
      "lib/internal/bootstrap/switches/does_own_process_state.js",
      "lib/internal/bootstrap/switches/is_main_thread.js",
      "lib/internal/test/binding.js",
      "lib/internal/util/types.js",
      "lib/internal/util/inspector.js",
      "lib/internal/util/comparisons.js",
      "lib/internal/util/debuglog.js",
      "lib/internal/util/inspect.js",
      "lib/internal/util/iterable_weak_map.js",
      "lib/internal/timers/promises.js",
      "lib/internal/streams/duplexpair.js",
      "lib/internal/streams/destroy.js",
      "lib/internal/streams/legacy.js",
      "lib/internal/streams/passthrough.js",
      "lib/internal/streams/readable.js",
      "lib/internal/streams/from.js",
      "lib/internal/streams/writable.js",
      "lib/internal/streams/state.js",
      "lib/internal/streams/buffer_list.js",
      "lib/internal/streams/end-of-stream.js",
      "lib/internal/streams/utils.js",
      "lib/internal/streams/transform.js",
      "lib/internal/streams/lazy_transform.js",
      "lib/internal/streams/duplex.js",
      "lib/internal/streams/pipeline.js",
      "lib/internal/readline/utils.js",
      "lib/internal/repl/history.js",
      "lib/internal/repl/utils.js",
      "lib/internal/repl/await.js",
      "lib/internal/assert/calltracker.js",
      "lib/internal/assert/assertion_error.js",
      "lib/internal/http2/util.js",
      "lib/internal/http2/core.js",
      "lib/internal/http2/compat.js",
      "lib/internal/per_context/messageport.js",
      "lib/internal/per_context/primordials.js",
      "lib/internal/per_context/domexception.js",
      "lib/internal/vm/module.js",
      "lib/internal/child_process/serialization.js",
      "lib/internal/debugger/inspect_repl.js",
      "lib/internal/debugger/inspect_client.js",
      "lib/internal/debugger/inspect.js",
      "lib/internal/worker/io.js",
      "lib/internal/worker/js_transferable.js",
      "lib/internal/main/repl.js",
      "lib/internal/main/print_help.js",
      "lib/internal/main/eval_string.js",
      "lib/internal/main/check_syntax.js",
      "lib/internal/main/prof_process.js",
      "lib/internal/main/worker_thread.js",
      "lib/internal/main/inspect.js",
      "lib/internal/main/eval_stdin.js",
      "lib/internal/main/run_main_module.js",
      "lib/internal/main/run_third_party_main.js",
      "lib/internal/modules/run_main.js",
      "lib/internal/modules/package_json_reader.js",
      "lib/internal/modules/esm/module_job.js",
      "lib/internal/modules/esm/get_source.js",
      "lib/internal/modules/esm/translators.js",
      "lib/internal/modules/esm/resolve.js",
      "lib/internal/modules/esm/create_dynamic_module.js",
      "lib/internal/modules/esm/handle_process_exit.js",
      "lib/internal/modules/esm/module_map.js",
      "lib/internal/modules/esm/get_format.js",
      "lib/internal/modules/esm/transform_source.js",
      "lib/internal/modules/esm/loader.js",
      "lib/internal/modules/cjs/helpers.js",
      "lib/internal/modules/cjs/loader.js",
      "lib/internal/source_map/source_map.js",
      "lib/internal/source_map/prepare_stack_trace.js",
      "lib/internal/source_map/source_map_cache.js",
      "lib/internal/dns/promises.js",
      "lib/internal/dns/utils.js",
      "lib/internal/fs/watchers.js",
      "lib/internal/fs/promises.js",
      "lib/internal/fs/read_file_context.js",
      "lib/internal/fs/rimraf.js",
      "lib/internal/fs/sync_write_stream.js",
      "lib/internal/fs/dir.js",
      "lib/internal/fs/streams.js",
      "lib/internal/fs/utils.js",
      "lib/internal/policy/manifest.js",
      "lib/internal/policy/sri.js",
      "lib/internal/process/task_queues.js",
      "lib/internal/process/per_thread.js",
      "lib/internal/process/warning.js",
      "lib/internal/process/policy.js",
      "lib/internal/process/promises.js",
      "lib/internal/process/signal.js",
      "lib/internal/process/execution.js",
      "lib/internal/process/esm_loader.js",
      "lib/internal/process/report.js",
      "lib/internal/process/worker_thread_only.js",
      "lib/internal/console/constructor.js",
      "lib/internal/console/global.js",
      "lib/fs/promises.js"
    ],
    "node_module_version": 83,
    "node_no_browser_globals": "false",
    "node_prefix": "/",
    "node_release_urlbase": "https://nodejs.org/download/release/",
    "node_shared": "false",
    "node_shared_brotli": "false",
    "node_shared_cares": "false",
    "node_shared_http_parser": "false",
    "node_shared_libuv": "false",
    "node_shared_nghttp2": "false",
    "node_shared_openssl": "false",
    "node_shared_zlib": "false",
    "node_tag": "",
    "node_target_type": "executable",
    "node_use_bundled_v8": "true",
    "node_use_dtrace": "true",
    "node_use_etw": "false",
    "node_use_node_code_cache": "true",
    "node_use_node_snapshot": "true",
    "node_use_openssl": "true",
    "node_use_v8_platform": "true",
    "node_with_ltcg": "false",
    "node_without_node_options": "false",
    "openssl_fips": "",
    "openssl_is_fips": "false",
    "ossfuzz": "false",
    "shlib_suffix": "83.dylib",
    "target_arch": "x64",
    "v8_enable_31bit_smis_on_64bit_arch": 0,
    "v8_enable_gdbjit": 0,
    "v8_enable_i18n_support": 1,
    "v8_enable_inspector": 1,
    "v8_enable_lite_mode": 0,
    "v8_enable_object_print": 1,
    "v8_enable_pointer_compression": 0,
    "v8_no_strict_aliasing": 1,
    "v8_optimized_debug": 1,
    "v8_promise_internal_field_count": 1,
    "v8_random_seed": 0,
    "v8_trace_maps": 0,
    "v8_use_siphash": 1,
    "want_separate_host_toolset": 0,
    "xcode_version": "11.0",
    "nodedir": "/Users/<USER>/Library/Caches/node-gyp/14.21.2",
    "standalone_static_library": 1,
    "dry_run": "",
    "legacy_bundling": "",
    "save_dev": "",
    "browser": "",
    "commit_hooks": "true",
    "only": "",
    "viewer": "man",
    "also": "",
    "rollback": "true",
    "sign_git_commit": "",
    "audit": "true",
    "usage": "",
    "globalignorefile": "/Users/<USER>/.nvm/versions/node/v14.21.2/etc/npmignore",
    "init_author_url": "",
    "maxsockets": "50",
    "shell": "/bin/zsh",
    "metrics_registry": "https://registry.npmjs.org/",
    "parseable": "",
    "shrinkwrap": "true",
    "init_license": "ISC",
    "timing": "",
    "if_present": "",
    "cache_max": "Infinity",
    "init_author_email": "",
    "sign_git_tag": "",
    "cert": "",
    "git_tag_version": "true",
    "local_address": "",
    "long": "",
    "preid": "",
    "fetch_retries": "2",
    "registry": "https://registry.npmjs.org/",
    "key": "",
    "message": "%s",
    "versions": "",
    "globalconfig": "/Users/<USER>/.nvm/versions/node/v14.21.2/etc/npmrc",
    "always_auth": "",
    "logs_max": "10",
    "prefer_online": "",
    "cache_lock_retries": "10",
    "global_style": "",
    "update_notifier": "true",
    "audit_level": "low",
    "heading": "npm",
    "fetch_retry_mintimeout": "10000",
    "offline": "",
    "read_only": "",
    "searchlimit": "20",
    "access": "",
    "json": "",
    "allow_same_version": "",
    "description": "true",
    "engine_strict": "",
    "https_proxy": "",
    "init_module": "/Users/<USER>/.npm-init.js",
    "userconfig": "/Users/<USER>/.npmrc",
    "cidr": "",
    "node_version": "14.21.2",
    "user": "501",
    "auth_type": "legacy",
    "editor": "vi",
    "ignore_prepublish": "",
    "save": "true",
    "script_shell": "",
    "tag": "latest",
    "before": "",
    "global": "",
    "progress": "true",
    "ham_it_up": "",
    "optional": "true",
    "searchstaleness": "900",
    "bin_links": "true",
    "force": "",
    "save_prod": "",
    "searchopts": "",
    "depth": "Infinity",
    "node_gyp": "/Users/<USER>/.nvm/versions/node/v14.21.2/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js",
    "rebuild_bundle": "true",
    "sso_poll_frequency": "500",
    "unicode": "true",
    "fetch_retry_maxtimeout": "60000",
    "ca": "",
    "save_prefix": "^",
    "scripts_prepend_node_path": "warn-only",
    "sso_type": "oauth",
    "strict_ssl": "true",
    "tag_version_prefix": "v",
    "dev": "",
    "fetch_retry_factor": "10",
    "group": "20",
    "save_exact": "",
    "cache_lock_stale": "60000",
    "prefer_offline": "",
    "version": "",
    "cache_min": "10",
    "otp": "",
    "cache": "/Users/<USER>/.npm",
    "searchexclude": "",
    "color": "true",
    "package_lock": "true",
    "fund": "true",
    "package_lock_only": "",
    "save_optional": "",
    "user_agent": "npm/6.14.17 node/v14.21.2 darwin x64",
    "ignore_scripts": "",
    "cache_lock_wait": "10000",
    "production": "",
    "save_bundle": "",
    "send_metrics": "",
    "init_version": "1.0.0",
    "node_options": "",
    "umask": "0022",
    "scope": "",
    "git": "git",
    "init_author_name": "",
    "onload_script": "",
    "tmp": "/var/folders/19/z2_6y2616596hhcpnfmn41rm0000gn/T",
    "unsafe_perm": "true",
    "format_package_lock": "true",
    "link": "",
    "prefix": "/Users/<USER>/.nvm/versions/node/v14.21.2"
  }
}
