{"_from": "hsl-regex@^1.0.0", "_id": "hsl-regex@1.0.0", "_inBundle": false, "_integrity": "sha512-M5ezZw4LzXbBKMruP+BNANf0k+19hDQMgpzBIYnya//Al+fjNct9Wf3b1WedLqdEs2hKBvxq/jh+DsHJLj0F9A==", "_location": "/hsl-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "hsl-regex@^1.0.0", "name": "hsl-regex", "escapedName": "hsl-regex", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/is-color-stop"], "_resolved": "https://registry.npmjs.org/hsl-regex/-/hsl-regex-1.0.0.tgz", "_shasum": "d49330c789ed819e276a4c0d272dffa30b18fe6e", "_spec": "hsl-regex@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/is-color-stop", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/regexps/hsl-regex/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Regex for matching HSL colors.", "devDependencies": {"mocha": "*"}, "directories": {"test": "test"}, "homepage": "https://github.com/regexps/hsl-regex", "keywords": ["hsl", "regex", "regexp", "color", "css"], "license": "MIT", "main": "index.js", "name": "hsl-regex", "repository": {"type": "git", "url": "git+https://github.com/regexps/hsl-regex.git"}, "scripts": {"test": "mocha test"}, "version": "1.0.0"}