{"_from": "hex-color-regex@^1.1.0", "_id": "hex-color-regex@1.1.0", "_inBundle": false, "_integrity": "sha512-l9sfDFsuqtOqKDsQdqrMRk0U85RZc0RtOR9yPI7mRVOa4FsR/BVnZ0shmQRM96Ji99kYZP/7hn1cedc1+ApsTQ==", "_location": "/hex-color-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "hex-color-regex@^1.1.0", "name": "hex-color-regex", "escapedName": "hex-color-regex", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/is-color-stop"], "_resolved": "https://registry.npmjs.org/hex-color-regex/-/hex-color-regex-1.1.0.tgz", "_shasum": "4c06fccb4602fe2602b3c93df82d7e7dbf1a8a8e", "_spec": "hex-color-regex@^1.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/is-color-stop", "author": {"name": "<PERSON><PERSON><PERSON> Reagent", "email": "@tunnckoCore", "url": "http://www.tunnckocore.tk"}, "bugs": {"url": "https://github.com/regexps/hex-color-regex/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "The best regular expression (regex) for matching hex color values from string.", "devDependencies": {"mukla": "^0.4.9"}, "homepage": "https://github.com/regexps/hex-color-regex#readme", "keywords": ["color", "colors", "css", "expr", "expression", "expressions", "hex", "match", "matching", "regex", "regexp", "regexps", "regular", "web"], "license": "MIT", "main": "index.js", "name": "hex-color-regex", "repository": {"type": "git", "url": "git+https://github.com/regexps/hex-color-regex.git"}, "scripts": {"test": "standard && node test.js"}, "version": "1.1.0"}