{"_from": "hmac-drbg@^1.0.1", "_id": "hmac-drbg@1.0.1", "_inBundle": false, "_integrity": "sha512-Tti3gMqLdZfhOQY1Mzf/AanLiqh1WTiJgEj26ZuYQ9fbkLomzGchCws4FyrSd4VkpBfiNhaE1On+lOz894jvXg==", "_location": "/hmac-drbg", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "hmac-drbg@^1.0.1", "name": "hmac-drbg", "escapedName": "hmac-drbg", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/elliptic"], "_resolved": "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "_shasum": "d2745701025a6c775a6c545793ed502fc0c649a1", "_spec": "hmac-drbg@^1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/elliptic", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/indutny/hmac-drbg/issues"}, "bundleDependencies": false, "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}, "deprecated": false, "description": "Deterministic random bit generator (hmac)", "devDependencies": {"mocha": "^3.2.0"}, "homepage": "https://github.com/indutny/hmac-drbg#readme", "keywords": ["hmac", "drbg", "prng"], "license": "MIT", "main": "lib/hmac-drbg.js", "name": "hmac-drbg", "repository": {"type": "git", "url": "git+ssh://**************/indutny/hmac-drbg.git"}, "scripts": {"test": "mocha --reporter=spec test/*-test.js"}, "version": "1.0.1"}