{"_from": "param-case@2.1.x", "_id": "param-case@2.1.1", "_inBundle": false, "_integrity": "sha512-eQE845L6ot89sk2N8liD8HAuH4ca6Vvr7VWAWwt7+kvvG5aBcPmmphQ68JsEG2qa9n1TykS2DLeMt363AAH8/w==", "_location": "/param-case", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "param-case@2.1.x", "name": "param-case", "escapedName": "param-case", "rawSpec": "2.1.x", "saveSpec": null, "fetchSpec": "2.1.x"}, "_requiredBy": ["/html-minifier"], "_resolved": "https://registry.npmjs.org/param-case/-/param-case-2.1.1.tgz", "_shasum": "df94fd8cf6531ecf75e6bef9a0858fbc72be2247", "_spec": "param-case@2.1.x", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/html-minifier", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "bugs": {"url": "https://github.com/blakeembrey/param-case/issues"}, "bundleDependencies": false, "dependencies": {"no-case": "^2.2.0"}, "deprecated": false, "description": "Param case a string", "devDependencies": {"istanbul": "^0.4.3", "mocha": "^3.2.0", "standard": "^9.0.1"}, "files": ["param-case.js", "param-case.d.ts", "LICENSE"], "homepage": "https://github.com/blakeembrey/param-case", "keywords": ["param", "case", "dash", "hyphen"], "license": "MIT", "main": "param-case.js", "name": "param-case", "repository": {"type": "git", "url": "git://github.com/blakeembrey/param-case.git"}, "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test-std": "mocha -- -R spec --bail"}, "typings": "param-case.d.ts", "version": "2.1.1"}