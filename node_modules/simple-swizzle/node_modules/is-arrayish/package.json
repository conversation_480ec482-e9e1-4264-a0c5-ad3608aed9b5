{"_args": [["is-arrayish@0.3.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "is-arrayish@0.3.2", "_id": "is-arrayish@0.3.2", "_inBundle": false, "_integrity": "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==", "_location": "/simple-swizzle/is-arrayish", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "is-arrayish@0.3.2", "name": "is-arrayish", "escapedName": "is-arrayish", "rawSpec": "0.3.2", "saveSpec": null, "fetchSpec": "0.3.2"}, "_requiredBy": ["/simple-swizzle"], "_resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz", "_spec": "0.3.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "bugs": {"url": "https://github.com/qix-/node-is-arrayish/issues"}, "description": "Determines if an object can be used as an array", "devDependencies": {"@zeit/eslint-config-node": "^0.3.0", "@zeit/git-hooks": "^0.1.4", "coffeescript": "^2.3.1", "coveralls": "^3.0.1", "eslint": "^4.19.1", "istanbul": "^0.4.5", "mocha": "^5.2.0", "should": "^13.2.1"}, "eslintConfig": {"extends": ["@zeit/eslint-config-node"]}, "git": {"pre-commit": "lint-staged"}, "homepage": "https://github.com/qix-/node-is-arrayish#readme", "keywords": ["is", "array", "duck", "type", "arrayish", "similar", "proto", "prototype", "type"], "license": "MIT", "name": "is-arrayish", "repository": {"type": "git", "url": "git+https://github.com/qix-/node-is-arrayish.git"}, "scripts": {"lint": "zeit-eslint --ext .jsx,.js .", "lint-staged": "git diff --diff-filter=ACMRT --cached --name-only '*.js' '*.jsx' | xargs zeit-eslint", "test": "mocha --require coffeescript/register ./test/**/*.coffee"}, "version": "0.3.2"}