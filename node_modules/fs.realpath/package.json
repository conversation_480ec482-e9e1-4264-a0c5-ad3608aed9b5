{"_args": [["fs.realpath@1.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "fs.realpath@1.0.0", "_id": "fs.realpath@1.0.0", "_inBundle": false, "_integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "_location": "/fs.realpath", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "fs.realpath@1.0.0", "name": "fs.realpath", "escapedName": "fs.realpath", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/glob", "/globule/glob"], "_resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "_spec": "1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/fs.realpath/issues"}, "dependencies": {}, "description": "Use node's fs.realpath, but fall back to the JS implementation if the native one fails", "devDependencies": {}, "files": ["old.js", "index.js"], "homepage": "https://github.com/isaacs/fs.realpath#readme", "keywords": ["realpath", "fs", "polyfill"], "license": "ISC", "main": "index.js", "name": "fs.realpath", "repository": {"type": "git", "url": "git+https://github.com/isaacs/fs.realpath.git"}, "scripts": {"test": "tap test/*.js --cov"}, "version": "1.0.0"}