{"_from": "merge-options@1.0.1", "_id": "merge-options@1.0.1", "_inBundle": false, "_integrity": "sha512-iuPV41VWKWBIOpBsjoxjDZw8/GbSfZ2mk7N1453bwMrfzdrIk7EzBd+8UVR6rkw67th7xnk9Dytl3J+lHPdxvg==", "_location": "/merge-options", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "merge-options@1.0.1", "name": "merge-options", "escapedName": "merge-options", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/posthtml-svg-mode", "/svg-baker"], "_resolved": "https://registry.npmjs.org/merge-options/-/merge-options-1.0.1.tgz", "_shasum": "2a64b24457becd4e4dc608283247e94ce589aa32", "_spec": "merge-options@1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/svg-baker", "author": {"name": "<PERSON>", "email": "micha<PERSON>@schnittstabil.de", "url": "schnittstabil.de"}, "bugs": {"url": "https://github.com/schnittstabil/merge-options/issues"}, "bundleDependencies": false, "dependencies": {"is-plain-obj": "^1.1"}, "deprecated": false, "description": "Merge Option Objects", "devDependencies": {"ava": "^0.25", "coveralls": "^3.0", "nyc": "^11.7", "rimraf": "^2.5", "xo": "^0.20"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/schnittstabil/merge-options#readme", "keywords": ["merge", "options", "deep", "plain", "object", "extend", "clone"], "license": "MIT", "name": "merge-options", "repository": {"type": "git", "url": "git+https://github.com/schnittstabil/merge-options.git"}, "scripts": {"clean": "rimraf .nyc_output/ coverage/", "coverage-html": "nyc ava && nyc report --reporter=html", "test": "xo && nyc ava"}, "version": "1.0.1"}