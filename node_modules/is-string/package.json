{"_args": [["is-string@1.1.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "is-string@1.1.1", "_id": "is-string@1.1.1", "_inBundle": false, "_integrity": "sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==", "_location": "/is-string", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "is-string@1.1.1", "name": "is-string", "escapedName": "is-string", "rawSpec": "1.1.1", "saveSpec": null, "fetchSpec": "1.1.1"}, "_requiredBy": ["/array-includes", "/array.prototype.reduce", "/es-abstract", "/which-boxed-primitive"], "_resolved": "https://registry.npmjs.org/is-string/-/is-string-1.1.1.tgz", "_spec": "1.1.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "bugs": {"url": "https://github.com/inspect-js/is-string/issues"}, "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "description": "Is this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/core-js": "^2.5.8", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "core-js": "^3.39.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "is": "^3.3.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-string#readme", "keywords": ["String", "string", "ES6", "toStringTag", "@@toStringTag", "String object"], "license": "MIT", "main": "index.js", "name": "is-string", "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-string.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:corejs", "test:corejs": "nyc tape test-corejs.js", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.1.1"}