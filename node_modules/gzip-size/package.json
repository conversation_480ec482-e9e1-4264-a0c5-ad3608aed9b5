{"_from": "gzip-size@^4.1.0", "_id": "gzip-size@4.1.0", "_inBundle": false, "_integrity": "sha512-1g6EPVvIHuPmpAdBBpsIVYLgjzGV/QqcFRJXpMyrqEWG10JhOaTjQeCcjMDyX0Iqfm/Q5M9twR/mbDk5f5MqkA==", "_location": "/gzip-size", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "gzip-size@^4.1.0", "name": "gzip-size", "escapedName": "gzip-size", "rawSpec": "^4.1.0", "saveSpec": null, "fetchSpec": "^4.1.0"}, "_requiredBy": ["/webpack-bundle-analyzer"], "_resolved": "https://registry.npmjs.org/gzip-size/-/gzip-size-4.1.0.tgz", "_shasum": "8ae096257eabe7d69c45be2b67c448124ffb517c", "_spec": "gzip-size@^4.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/webpack-bundle-analyzer", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/gzip-size/issues"}, "bundleDependencies": false, "dependencies": {"duplexer": "^0.1.1", "pify": "^3.0.0"}, "deprecated": false, "description": "Get the gzipped size of a string or buffer", "devDependencies": {"ava": "*", "p-event": "^1.3.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/gzip-size#readme", "keywords": ["app", "tool", "zlib", "gzip", "compressed", "size", "string", "buffer"], "license": "MIT", "name": "gzip-size", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/gzip-size.git"}, "scripts": {"test": "xo && ava"}, "version": "4.1.0"}