{"_args": [["is-number-object@1.1.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "is-number-object@1.1.1", "_id": "is-number-object@1.1.1", "_inBundle": false, "_integrity": "sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==", "_location": "/is-number-object", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "is-number-object@1.1.1", "name": "is-number-object", "escapedName": "is-number-object", "rawSpec": "1.1.1", "saveSpec": null, "fetchSpec": "1.1.1"}, "_requiredBy": ["/which-boxed-primitive"], "_resolved": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.1.tgz", "_spec": "1.1.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-number-object/issues"}, "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "description": "Is this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/core-js": "^2.5.8", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "core-js": "^3.39.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "indexof": "^0.0.1", "is": "^3.3.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-number-object#readme", "keywords": ["Number", "ES6", "toStringTag", "@@toStringTag", "Number object"], "license": "MIT", "main": "index.js", "name": "is-number-object", "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-number-object.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:corejs", "test:corejs": "nyc tape test-corejs.js", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js"}, "version": "1.1.1"}