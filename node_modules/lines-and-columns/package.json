{"_args": [["lines-and-columns@1.2.4", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "lines-and-columns@1.2.4", "_id": "lines-and-columns@1.2.4", "_inBundle": false, "_integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==", "_location": "/lines-and-columns", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "lines-and-columns@1.2.4", "name": "lines-and-columns", "escapedName": "lines-and-columns", "rawSpec": "1.2.4", "saveSpec": null, "fetchSpec": "1.2.4"}, "_requiredBy": ["/parse-json"], "_resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "_spec": "1.2.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/eventualbuddha/lines-and-columns/issues"}, "description": "Maps lines and columns to character offsets and back.", "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "esbuild": "^0.13.15", "esbuild-runner": "^2.2.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "is-ci-cli": "^2.2.0", "jest": "^27.3.1", "prettier": "^2.4.1", "semantic-release": "^18.0.0", "typescript": "^4.5.2"}, "files": ["build"], "homepage": "https://github.com/eventualbuddha/lines-and-columns#readme", "keywords": ["lines", "columns", "parser"], "license": "MIT", "main": "./build/index.js", "name": "lines-and-columns", "repository": {"type": "git", "url": "git+https://github.com/eventualbuddha/lines-and-columns.git"}, "scripts": {"build:watch": "tsc --build tsconfig.build.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "is-ci test:coverage test:watch", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "types": "./build/index.d.ts", "version": "1.2.4"}