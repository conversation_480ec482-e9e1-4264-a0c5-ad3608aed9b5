{"_from": "lower-case@^1.1.1", "_id": "lower-case@1.1.4", "_inBundle": false, "_integrity": "sha512-2Fgx1Ycm599x+WGpIYwJOvsjmXFzTSc34IwDWALRA/8AopUKAVPwfJ+h5+f85BCp0PWmmJcWzEpxOpoXycMpdA==", "_location": "/lower-case", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lower-case@^1.1.1", "name": "lower-case", "escapedName": "lower-case", "rawSpec": "^1.1.1", "saveSpec": null, "fetchSpec": "^1.1.1"}, "_requiredBy": ["/no-case"], "_resolved": "https://registry.npmjs.org/lower-case/-/lower-case-1.1.4.tgz", "_shasum": "9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac", "_spec": "lower-case@^1.1.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/no-case", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "bugs": {"url": "https://github.com/blakeembrey/lower-case/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Lowercase a string", "devDependencies": {"istanbul": "^0.3.5", "mocha": "^2.1.0", "pre-commit": "^1.0.2", "standard": "^2.4.5"}, "files": ["lower-case.js", "lower-case.d.ts", "LICENSE"], "homepage": "https://github.com/blakeembrey/lower-case", "keywords": ["cases", "lower", "lowercase", "case"], "license": "MIT", "main": "lower-case.js", "name": "lower-case", "repository": {"type": "git", "url": "git://github.com/blakeembrey/lower-case.git"}, "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test-std": "mocha -- -R spec --bail"}, "standard": {"ignore": ["coverage/**", "node_modules/**", "bower_components/**"]}, "typings": "lower-case.d.ts", "version": "1.1.4"}