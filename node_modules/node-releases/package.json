{"_args": [["node-releases@2.0.19", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "node-releases@2.0.19", "_id": "node-releases@2.0.19", "_inBundle": false, "_integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "_location": "/node-releases", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "node-releases@2.0.19", "name": "node-releases", "escapedName": "node-releases", "rawSpec": "2.0.19", "saveSpec": null, "fetchSpec": "2.0.19"}, "_requiredBy": ["/browserslist"], "_resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "_spec": "2.0.19", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/chicoxyzzy/node-releases/issues"}, "description": "Node.js releases data", "devDependencies": {"semver": "^7.3.5"}, "homepage": "https://github.com/chicoxyzzy/node-releases#readme", "keywords": ["nodejs", "releases"], "license": "MIT", "name": "node-releases", "repository": {"type": "git", "url": "git+https://github.com/chicoxyzzy/node-releases.git"}, "scripts": {"build": "node scripts/build.js"}, "type": "module", "version": "2.0.19"}