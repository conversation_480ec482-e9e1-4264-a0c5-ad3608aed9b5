{"_from": "isobject@^3.0.1", "_id": "isobject@3.0.1", "_inBundle": false, "_integrity": "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==", "_location": "/isobject", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "isobject@^3.0.1", "name": "isobject", "escapedName": "isobject", "rawSpec": "^3.0.1", "saveSpec": null, "fetchSpec": "^3.0.1"}, "_requiredBy": ["/base", "/braces", "/cache-base", "/class-utils", "/has-value", "/http-proxy-middleware/define-property", "/is-plain-object", "/nanomatch/define-property", "/object-visit", "/object.pick", "/snapdragon-node", "/to-regex/define-property", "/unset-value", "/watchpack-chokidar2/define-property", "/webpack-dev-server/define-property"], "_resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "_shasum": "4e431e92b11a9731636aa1f9c8d1ccbcfdab78df", "_spec": "isobject@^3.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "bundleDependencies": false, "contributors": [{"url": "https://github.com/LeSuisse"}, {"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/magnudae"}, {"name": "<PERSON>", "url": "https://macwright.org"}], "dependencies": {}, "deprecated": false, "description": "Returns true if the value is an object and not an array or null.", "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5"}, "engines": {"node": ">=0.10.0"}, "files": ["index.d.ts", "index.js"], "homepage": "https://github.com/jonschlinkert/isobject", "keywords": ["check", "is", "is-object", "isobject", "kind", "kind-of", "kindof", "native", "object", "type", "typeof", "value"], "license": "MIT", "main": "index.js", "name": "isobject", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/isobject.git"}, "scripts": {"test": "mocha"}, "types": "index.d.ts", "verb": {"related": {"list": ["extend-shallow", "is-plain-object", "kind-of", "merge-deep"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "version": "3.0.1"}