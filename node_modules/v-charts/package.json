{"_args": [["v-charts@1.19.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "v-charts@1.19.0", "_id": "v-charts@1.19.0", "_inBundle": false, "_integrity": "sha512-vm2HBUmxAsXK0ivwce9LytcpqrItDA5JSPLYVxZXtiuoyhcn80XX1/3dPJd/1GqG1OYv3jfBo1s9ra4q8GowqA==", "_location": "/v-charts", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "v-charts@1.19.0", "name": "v-charts", "escapedName": "v-charts", "rawSpec": "1.19.0", "saveSpec": null, "fetchSpec": "1.19.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/v-charts/-/v-charts-1.19.0.tgz", "_spec": "1.19.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "xiguaxigua"}, "bugs": {"url": "https://github.com/ElemeFE/v-charts/issues"}, "dependencies": {"echarts-amap": "1.0.0-rc.6", "echarts-liquidfill": "^2.0.2", "echarts-wordcloud": "^1.1.3", "numerify": "1.2.9", "utils-lite": "0.1.10"}, "description": "Vue Echarts Components", "devDependencies": {"autoprefixer": "^8.6.3", "babel-core": "^6.26.3", "babel-eslint": "^8.2.3", "babel-loader": "^7.1.4", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-object-assign": "^6.22.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.24.1", "conventional-changelog-cli": "^2.0.1", "css-loader": "^0.28.11", "cssnano": "^3.10.0", "docsify-cli": "^4.2.1", "echarts": "^4.1.0", "es6-promise": "^4.2.4", "eslint": "^4.19.1", "eslint-config-standard": "^11.0.0", "eslint-friendly-formatter": "^4.0.1", "eslint-loader": "^2.0.0", "eslint-plugin-html": "^4.0.3", "eslint-plugin-import": "^2.12.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "gh-pages": "^1.2.0", "html-webpack-plugin": "^3.2.0", "jasmine-core": "^3.1.0", "karma": "^2.0.3", "karma-babel-preprocessor": "^7.0.0", "karma-chrome-launcher": "^2.2.0", "karma-commonjs": "^1.0.0", "karma-jasmine": "^1.1.2", "karma-phantomjs-launcher": "^1.0.4", "karma-spec-reporter": "^0.0.32", "karma-webpack": "^3.0.0", "less": "^3.0.4", "less-loader": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "prismjs": "^1.15.0", "rollup": "^0.60.1", "rollup-plugin-babel": "^3.0.4", "rollup-plugin-commonjs": "^9.1.3", "rollup-plugin-eslint": "^4.0.0", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-uglify": "^4.0.0", "rollup-plugin-vue": "2.5.2", "style-loader": "^0.21.0", "uglify-es": "^3.3.9", "vue": "^2.5.16", "vue-loader": "^15.2.4", "vue-router": "^3.0.1", "vue-style-loader": "^4.1.0", "vue-template-compiler": "^2.5.16", "webpack": "^4.11.1", "webpack-cli": "^3.0.8", "webpack-dev-server": "^3.1.4"}, "homepage": "https://v-charts.js.org", "keywords": ["vue", "echarts"], "license": "MIT", "main": "lib/index.js", "name": "v-charts", "peerDependencies": {"echarts": ">3.0.0", "vue": ">2.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/ElemeFE/v-charts.git"}, "scripts": {"build": "rm -f -r lib && node build/rollup.config.js && cp -f lib/index.min.js lib/style.min.css docs/", "buildOnly": "rm -f -r lib && node build/rollup.config.js", "deploy": "gh-pages -d docs", "dev": "webpack-dev-server --inline --progress --config ./build/webpack.config.js", "docs": "docsify serve docs", "prepublishOnly": "npm run build", "test": "karma start ./test/karma.conf.js", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && conventional-changelog -p angular -i CHANGELOG_CN.md -s && git add CHANGELOG.md CHANGELOG_CN.md"}, "version": "1.19.0"}