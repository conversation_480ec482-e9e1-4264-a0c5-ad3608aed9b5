{"_from": "hsla-regex@^1.0.0", "_id": "hsla-regex@1.0.0", "_inBundle": false, "_integrity": "sha512-7Wn5GMLuHBjZCb2bTmnDOycho0p/7UVaAeqXZGbHrBCl6Yd/xDhQJAXe6Ga9AXJH2I5zY1dEdYw2u1UptnSBJA==", "_location": "/hsla-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "hsla-regex@^1.0.0", "name": "hsla-regex", "escapedName": "hsla-regex", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/is-color-stop"], "_resolved": "https://registry.npmjs.org/hsla-regex/-/hsla-regex-1.0.0.tgz", "_shasum": "c1ce7a3168c8c6614033a4b5f7877f3b225f9c38", "_spec": "hsla-regex@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/is-color-stop", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/regexps/hsla-regex/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Regex for matching HSLA colors.", "devDependencies": {"mocha": "*"}, "directories": {"test": "test"}, "homepage": "https://github.com/regexps/hsla-regex", "keywords": ["hsla", "regex", "regexp", "color", "css"], "license": "MIT", "main": "index.js", "name": "hsla-regex", "repository": {"type": "git", "url": "git+https://github.com/regexps/hsla-regex.git"}, "scripts": {"test": "mocha test"}, "version": "1.0.0"}