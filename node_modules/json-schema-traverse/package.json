{"_args": [["json-schema-traverse@0.4.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "json-schema-traverse@0.4.1", "_id": "json-schema-traverse@0.4.1", "_inBundle": false, "_integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "_location": "/json-schema-traverse", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "json-schema-traverse@0.4.1", "name": "json-schema-traverse", "escapedName": "json-schema-traverse", "rawSpec": "0.4.1", "saveSpec": null, "fetchSpec": "0.4.1"}, "_requiredBy": ["/ajv"], "_resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "_spec": "0.4.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/epoberezkin/json-schema-traverse/issues"}, "description": "Traverse JSON Schema passing each schema object to callback", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "keywords": ["JSON-Schema", "traverse", "iterate"], "license": "MIT", "main": "index.js", "name": "json-schema-traverse", "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "scripts": {"eslint": "eslint index.js spec", "test": "npm run eslint && nyc npm run test-spec", "test-spec": "mocha spec -R spec"}, "version": "0.4.1"}