{"_from": "has-unicode@^2.0.1", "_id": "has-unicode@2.0.1", "_inBundle": false, "_integrity": "sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==", "_location": "/has-unicode", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "has-unicode@^2.0.1", "name": "has-unicode", "escapedName": "has-unicode", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/gauge", "/npmlog/gauge"], "_resolved": "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz", "_shasum": "e0e6fe6a28cf51138855e086d1691e771de2a8b9", "_spec": "has-unicode@^2.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/gauge", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Try to guess if your terminal supports unicode", "devDependencies": {"require-inject": "^1.3.0", "tap": "^2.3.1"}, "files": ["index.js"], "homepage": "https://github.com/iarna/has-unicode", "keywords": ["unicode", "terminal"], "license": "ISC", "main": "index.js", "name": "has-unicode", "repository": {"type": "git", "url": "git+https://github.com/iarna/has-unicode.git"}, "scripts": {"test": "tap test/*.js"}, "version": "2.0.1"}