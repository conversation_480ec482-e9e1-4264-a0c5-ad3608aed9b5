{"_from": "caniuse-db@^1.0.30000634", "_id": "caniuse-db@1.0.30001731", "_inBundle": false, "_integrity": "sha512-IbYSXiOfvIJmCRLkrE/hMSsTZTu48NBddgIgC027NnuPwu/V14PnO3UtHxoQGSA16b09zZJkCsaoLbwMSllZrA==", "_location": "/caniuse-db", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "caniuse-db@^1.0.30000634", "name": "caniuse-db", "escapedName": "caniuse-db", "rawSpec": "^1.0.30000634", "saveSpec": null, "fetchSpec": "^1.0.30000634"}, "_requiredBy": ["/caniuse-api", "/caniuse-api/browserslist", "/cssnano/autoprefixer", "/cssnano/browserslist", "/postcss-merge-rules/browserslist"], "_resolved": "https://registry.npmjs.org/caniuse-db/-/caniuse-db-1.0.30001731.tgz", "_shasum": "2e7dee347ebd0cb7423e1dd1d498713869cb638b", "_spec": "caniuse-db@^1.0.30000634", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/cssnano/node_modules/autoprefixer", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Fyrd/caniuse/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Raw browser/feature support data from caniuse.com", "homepage": "https://github.com/Fyrd/caniuse#readme", "keywords": ["support", "css", "js", "html5", "svg"], "license": "CC-BY-4.0", "name": "caniuse-db", "repository": {"type": "git", "url": "git+https://github.com/Fyrd/caniuse.git"}, "scripts": {"validate": "node validator/validate-jsons.js"}, "version": "1.0.30001731"}