# caniuse
[![Build Status](https://github.com/Fyrd/caniuse/actions/workflows/main.yml/badge.svg?branch=main)](https://github.com/Fyrd/caniuse/actions/)

## About

This repo contains raw data from the caniuse.com support tables. It serves two purposes:

1. The ability for anyone interested to update or add to the support data on the site. If you are interested in this, please read the [CONTRIBUTING file](CONTRIBUTING.md).

2. Access to the site's data for other projects. For this use the [`fulldata-json/data-2.0.json`](fulldata-json/data-2.0.json) file which includes all support data. ([`data.json`](data.json) is also available for backwards compatibility.)

The data in this repo is available for use under a CC BY 4.0 license (https://creativecommons.org/licenses/by/4.0/). For attribution just mention somewhere that the source is caniuse.com. If you have any questions about using the data for your project please contact me here: https://a.deveria.com/contact

Some of the data on caniuse.com is provided by [browser-compat-data](https://github.com/mdn/browser-compat-data) instead of this repository.

## Sponsor

[![Browserstack](/browserstack-logo.png)](https://browserstack.com)


Caniuse.com is created and maintained by Alexis Deveria
<br>https://caniuse.com/
<br>https://a.deveria.com/
