{"_from": "is-accessor-descriptor@^1.0.1", "_id": "is-accessor-descriptor@1.0.1", "_inBundle": false, "_integrity": "sha512-YBUanLI8Yoihw923YeFUS5fs0fF2f5TSFTNiYAAzhhDscDa3lEqYuz1pDOEP5KvX94I9ey3vsqjJcLVFVU+3QA==", "_location": "/is-accessor-descriptor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-accessor-descriptor@^1.0.1", "name": "is-accessor-descriptor", "escapedName": "is-accessor-descriptor", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/define-property/is-descriptor", "/http-proxy-middleware/is-descriptor", "/is-descriptor", "/nanomatch/is-descriptor", "/to-regex/is-descriptor", "/watchpack-chokidar2/is-descriptor", "/webpack-dev-server/is-descriptor"], "_resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.1.tgz", "_shasum": "3223b10628354644b86260db29b3e693f5ceedd4", "_spec": "is-accessor-descriptor@^1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/is-descriptor", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-accessor-descriptor/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "dependencies": {"hasown": "^2.0.0"}, "deprecated": false, "description": "Returns true if a value has the characteristics of a valid JavaScript accessor descriptor.", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.2"}, "engines": {"node": ">= 0.10"}, "homepage": "https://github.com/inspect-js/is-accessor-descriptor", "keywords": ["descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "license": "MIT", "main": "index.js", "name": "is-accessor-descriptor", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-accessor-descriptor.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.0.1"}