{"_args": [["vue-quill-editor@3.0.6", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "vue-quill-editor@3.0.6", "_id": "vue-quill-editor@3.0.6", "_inBundle": false, "_integrity": "sha512-g20oSZNWg8Hbu41Kinjd55e235qVWPLfg4NvsLW6d+DhgBTFbEuMpcWlUdrD6qT3+Noim6DRu18VLM9lVShXOQ==", "_location": "/vue-quill-editor", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-quill-editor@3.0.6", "name": "vue-quill-editor", "escapedName": "vue-quill-editor", "rawSpec": "3.0.6", "saveSpec": null, "fetchSpec": "3.0.6"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vue-quill-editor/-/vue-quill-editor-3.0.6.tgz", "_spec": "3.0.6", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "Surmon", "email": "<EMAIL>", "url": "https://surmon.me"}, "bugs": {"url": "https://github.com/surmon-china/vue-quill-editor/issues"}, "dependencies": {"object-assign": "^4.1.1", "quill": "^1.3.4"}, "description": "Quill editor component for Vue", "devDependencies": {"autoprefixer": "^6.7.2", "babel-cli": "^6.23.0", "babel-core": "^6.24.1", "babel-eslint": "^7.1.1", "babel-helper-vue-jsx-merge-props": "^2.0.2", "babel-loader": "^6.2.10", "babel-plugin-istanbul": "^3.1.2", "babel-plugin-syntax-jsx": "^6.13.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.0.0", "chai": "^3.5.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.1.0", "copy-webpack-plugin": "^4.0.0", "cross-env": "^5.0.0", "cross-spawn": "^5.1.0", "css-loader": "^0.25.0", "eslint": "^3.14.1", "eslint-config-standard": "^6.1.0", "eslint-friendly-formatter": "^2.0.5", "eslint-loader": "^1.6.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.13.3", "extract-text-webpack-plugin": "^2.0.0-rc.3", "file-loader": "^0.10.0", "friendly-errors-webpack-plugin": "^1.1.3", "function-bind": "^1.1.0", "html-loader": "^0.4.4", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "inject-loader": "^2.0.1", "json-loader": "^0.5.4", "jstransformer-markdown-it": "^2.0.0", "karma": "^1.4.1", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon-chai": "^1.2.4", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.26", "karma-webpack": "^2.0.2", "lolex": "^1.5.2", "mocha": "^3.2.0", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^0.3.0", "phantomjs-prebuilt": "^2.1.3", "raw-loader": "^0.5.1", "semver": "^5.3.0", "shelljs": "^0.7.4", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "uglify-js": "^3.0.15", "url-loader": "^0.5.7", "vue": "^2.5.0", "vue-hot-reload-api": "^1.2.0", "vue-html-loader": "^1.0.0", "vue-loader": "^13.3.0", "vue-template-compiler": "^2.5.2", "vue-template-es2015-compiler": "^1.6.0", "webpack": "^2.2.1", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.16.1", "webpack-merge": "^2.6.1"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "expDependencies": {"node-sass": "^4.7.2", "sass-loader": "^6.0.6", "highlight.js": "^9.12.0", "quill-image-drop-module": "^1.0.3", "quill-image-resize-module": "^3.0.0", "vue-quill-editor": "^3.0.0"}, "files": ["dist", "src"], "homepage": "https://github.com/surmon-china/vue-quill-editor#readme", "jsnext:main": "dist/vue-quill-editor.js", "jspm": {"main": "dist/vue-quill-editor.js", "registry": "npm", "format": "esm"}, "keywords": ["vue-quill-editor", "vue quill", "vue text editor", "vue rich text editor", "vue web editor", "vue editor"], "license": "MIT", "main": "dist/vue-quill-editor.js", "name": "vue-quill-editor", "private": false, "repository": {"type": "git", "url": "git+https://github.com/surmon-china/vue-quill-editor.git"}, "scripts": {"build": "npm run build:spa && npm run build:ssr", "build:spa": "cross-env NODE_ENV=production webpack --config config/build.conf.js", "build:ssr": "babel src/ssr.js --out-file dist/ssr.js", "finish": "npm run lint && npm test && npm run build", "lint": "eslint --ext .js,.vue src test/unit/specs", "publish": "git push && git push --tags && npm publish", "test": "cross-env BABEL_ENV=test NODE_ENV=testing karma start test/unit/karma.conf.js --single-run", "unit": "cross-env BABEL_ENV=test NODE_ENV=testing karma start test/unit/karma.conf.js --watch"}, "unpkg": "dist/vue-quill-editor.js", "version": "3.0.6"}