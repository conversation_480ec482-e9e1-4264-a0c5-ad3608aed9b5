{"_from": "has-tostringtag@^1.0.2", "_id": "has-tostringtag@1.0.2", "_inBundle": false, "_integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "_location": "/has-tostringtag", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "has-tostringtag@^1.0.2", "name": "has-tostringtag", "escapedName": "has-tostringtag", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/es-set-tostringtag", "/is-arguments", "/is-async-function", "/is-boolean-object", "/is-date-object", "/is-generator-function", "/is-number-object", "/is-regex", "/is-string", "/which-builtin-type", "/which-typed-array"], "_resolved": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "_shasum": "2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc", "_spec": "has-tostringtag@^1.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/is-arguments", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/has-tostringtag/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "dependencies": {"has-symbols": "^1.0.3"}, "deprecated": false, "description": "Determine if the JS environment has `Symbol.toStringTag` support. Supports spec, or shams.", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/has-symbols": "^1.0.2", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "eslint": "=8.8.0", "get-own-property-symbols": "^0.9.5", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": [{"types": "./index.d.ts", "default": "./index.js"}, "./index.js"], "./shams": [{"types": "./shams.d.ts", "default": "./shams.js"}, "./shams.js"], "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/has-tostringtag#readme", "keywords": ["javascript", "ecmascript", "symbol", "symbols", "tostringtag", "Symbol.toStringTag"], "license": "MIT", "main": "index.js", "name": "has-tostringtag", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-tostringtag.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only", "test:shams": "npm run --silent test:shams:getownpropertysymbols && npm run --silent test:shams:corejs", "test:shams:corejs": "nyc node test/shams/core-js.js", "test:shams:getownpropertysymbols": "nyc node test/shams/get-own-property-symbols.js", "test:staging": "nyc node --harmony --es-staging test", "test:stock": "nyc node test", "tests-only": "npm run test:stock && npm run test:shams", "version": "auto-changelog && git add CHANGELOG.md"}, "types": "./index.d.ts", "version": "1.0.2"}