{"_args": [["postcss-discard-overridden@0.1.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "postcss-discard-overridden@0.1.1", "_id": "postcss-discard-overridden@0.1.1", "_inBundle": false, "_integrity": "sha512-IyKoDL8QNObOiUc6eBw8kMxBHCfxUaERYTUe2QF8k7j/xiirayDzzkmlR6lMQjrAM1p1DDRTvWrS7Aa8lp6/uA==", "_location": "/postcss-discard-overridden", "_phantomChildren": {"escape-string-regexp": "1.0.5", "has-ansi": "2.0.0", "js-base64": "2.6.4", "strip-ansi": "3.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "postcss-discard-overridden@0.1.1", "name": "postcss-discard-overridden", "escapedName": "postcss-discard-overridden", "rawSpec": "0.1.1", "saveSpec": null, "fetchSpec": "0.1.1"}, "_requiredBy": ["/cssnano"], "_resolved": "https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-0.1.1.tgz", "_spec": "0.1.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "ava": {"require": ["babel-register"]}, "bugs": {"url": "https://github.com/Justineo/postcss-discard-overridden/issues"}, "dependencies": {"postcss": "^5.0.16"}, "description": "PostCSS plugin to discard overridden @keyframes or @counter-style.", "devDependencies": {"ava": "^0.14.0", "babel-cli": "^6.7.7", "babel-plugin-add-module-exports": "^0.1.4", "babel-preset-es2015": "^6.6.0", "babel-register": "^6.7.2", "eslint": "^2.1.0", "eslint-config-postcss": "^2.0.0"}, "eslintConfig": {"extends": "eslint-config-postcss/es5"}, "homepage": "https://github.com/Justineo/postcss-discard-overridden", "keywords": ["postcss", "css", "postcss-plugin", "at-rules", "@keyframes", "@counter-style"], "license": "MIT", "main": "dist/index.js", "name": "postcss-discard-overridden", "repository": {"type": "git", "url": "git+https://github.com/Justineo/postcss-discard-overridden.git"}, "scripts": {"test": "ava && eslint *.js"}, "version": "0.1.1"}