{"_args": [["postcss@5.2.18", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "postcss@5.2.18", "_id": "postcss@5.2.18", "_inBundle": false, "_integrity": "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==", "_location": "/postcss-discard-overridden/postcss", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "postcss@5.2.18", "name": "postcss", "escapedName": "postcss", "rawSpec": "5.2.18", "saveSpec": null, "fetchSpec": "5.2.18"}, "_requiredBy": ["/postcss-discard-overridden"], "_resolved": "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz", "_spec": "5.2.18", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": {"fs": false}, "bugs": {"url": "https://github.com/postcss/postcss/issues"}, "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "description": "Tool for transforming styles with JS plugins", "devDependencies": {"ava": "^0.17.0", "babel-core": "^6.24.0", "babel-eslint": "^7.1.1", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0", "babel-preset-es2015": "^6.24.0", "chalk": "^1.1.3", "concat-with-sourcemaps": "^1.0.4", "del": "^2.2.2", "docdash": "^0.4.0", "eslint": "^3.18.0", "eslint-config-postcss": "^2.0.2", "fs-extra": "^1.0.0", "gulp": "^3.9.1", "gulp-ava": "^0.15.0", "gulp-babel": "^6.1.2", "gulp-changed": "^1.3.2", "gulp-eslint": "^3.0.1", "gulp-run": "^1.7.1", "gulp-sourcemaps": "^2.4.1", "jsdoc": "^3.4.3", "lint-staged": "^3.4.0", "postcss-parser-tests": "^5.0.11", "pre-commit": "^1.2.2", "run-sequence": "^1.2.2", "sinon": "^2.0.0", "strip-ansi": "^3.0.1", "yaspeller-ci": "^0.3.0"}, "engines": {"node": ">=0.12"}, "homepage": "http://postcss.org/", "keywords": ["css", "postcss", "rework", "preprocessor", "parser", "source map", "transform", "manipulation", "transpiler"], "license": "MIT", "lint-staged": {"test/*.js": "eslint", "lib/*.es6": "eslint", "*.md": "yaspeller-ci"}, "main": "lib/postcss", "name": "postcss", "pre-commit": ["lint-staged"], "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss.git"}, "scripts": {"lint-staged": "lint-staged", "test": "gulp"}, "types": "lib/postcss.d.ts", "version": "5.2.18"}