{"_args": [["lodash@4.17.21", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "lodash@4.17.21", "_id": "lodash@4.17.21", "_inBundle": false, "_integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "_location": "/lodash", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "lodash@4.17.21", "name": "lodash", "escapedName": "lodash", "rawSpec": "4.17.21", "saveSpec": null, "fetchSpec": "4.17.21"}, "_requiredBy": ["/babel-core", "/babel-generator", "/babel-register", "/babel-template", "/babel-traverse", "/babel-types"], "_resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "_spec": "4.17.21", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Lodash modular utilities.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "keywords": ["modules", "stdlib", "util"], "license": "MIT", "main": "lodash.js", "name": "lodash", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "version": "4.17.21"}