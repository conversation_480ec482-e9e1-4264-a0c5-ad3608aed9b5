{"_args": [["babel-code-frame@6.26.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "babel-code-frame@6.26.0", "_id": "babel-code-frame@6.26.0", "_inBundle": false, "_integrity": "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=", "_location": "/babel-code-frame", "_phantomChildren": {"escape-string-regexp": "1.0.5", "has-ansi": "2.0.0", "strip-ansi": "3.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "babel-code-frame@6.26.0", "name": "babel-code-frame", "escapedName": "babel-code-frame", "rawSpec": "6.26.0", "saveSpec": null, "fetchSpec": "6.26.0"}, "_requiredBy": ["/babel-core", "/babel-traverse"], "_resolved": "https://registry.npmjs.org/babel-code-frame/-/babel-code-frame-6.26.0.tgz", "_spec": "6.26.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"chalk": "^1.1.3", "esutils": "^2.0.2", "js-tokens": "^3.0.2"}, "description": "Generate errors that contain a code frame that point to source locations.", "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-code-frame", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame"}, "version": "6.26.0"}