{"_args": [["babel-generator@6.26.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "babel-generator@6.26.1", "_id": "babel-generator@6.26.1", "_inBundle": false, "_integrity": "sha512-HyfwY6ApZj7BYTcJURpM5tznulaBvyio7/0d4zFOeMPUmfxkCjHocCuoLa2SAGzBI8AREcH3eP3758F672DppA==", "_location": "/babel-generator", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-generator@6.26.1", "name": "babel-generator", "escapedName": "babel-generator", "rawSpec": "6.26.1", "saveSpec": null, "fetchSpec": "6.26.1"}, "_requiredBy": ["/babel-core"], "_resolved": "https://registry.npmjs.org/babel-generator/-/babel-generator-6.26.1.tgz", "_spec": "6.26.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "detect-indent": "^4.0.0", "jsesc": "^1.3.0", "lodash": "^4.17.4", "source-map": "^0.5.7", "trim-right": "^1.0.1"}, "description": "Turns an AST into code.", "devDependencies": {"babel-helper-fixtures": "^6.26.0", "babylon": "^6.18.0"}, "files": ["lib"], "homepage": "https://babeljs.io/", "license": "MIT", "main": "lib/index.js", "name": "babel-generator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-generator"}, "version": "6.26.1"}