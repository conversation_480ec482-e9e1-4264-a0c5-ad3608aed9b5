{"_from": "minimalistic-assert@^1.0.0", "_id": "minimalistic-assert@1.0.1", "_inBundle": false, "_integrity": "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==", "_location": "/minimalistic-assert", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "minimalistic-assert@^1.0.0", "name": "minimalistic-assert", "escapedName": "minimalistic-assert", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/asn1.js", "/des.js", "/elliptic", "/hash.js", "/hmac-drbg", "/wbuf"], "_resolved": "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "_shasum": "2e194de044626d4a10e7f7fbc00ce73e83e4d5c7", "_spec": "minimalistic-assert@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/des.js", "author": "", "bugs": {"url": "https://github.com/calvinmetcalf/minimalistic-assert/issues"}, "bundleDependencies": false, "deprecated": false, "description": "minimalistic-assert ===", "homepage": "https://github.com/calvinmetcalf/minimalistic-assert", "license": "ISC", "main": "index.js", "name": "minimalistic-assert", "repository": {"type": "git", "url": "git+https://github.com/calvinmetcalf/minimalistic-assert.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.1"}