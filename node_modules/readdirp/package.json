{"_args": [["readdirp@3.6.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "readdirp@3.6.0", "_id": "readdirp@3.6.0", "_inBundle": false, "_integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==", "_location": "/readdirp", "_optional": true, "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "readdirp@3.6.0", "name": "readdirp", "escapedName": "readdirp", "rawSpec": "3.6.0", "saveSpec": null, "fetchSpec": "3.6.0"}, "_requiredBy": ["/chokidar"], "_resolved": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "_spec": "3.6.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com"}, "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "dependencies": {"picomatch": "^2.2.1"}, "description": "Recursive version of fs.readdir with streaming API.", "devDependencies": {"@types/node": "^14", "chai": "^4.2", "chai-subset": "^1.6", "dtslint": "^3.3.0", "eslint": "^7.0.0", "mocha": "^7.1.1", "nyc": "^15.0.0", "rimraf": "^3.0.0", "typescript": "^4.0.3"}, "engines": {"node": ">=8.10.0"}, "eslintConfig": {"root": true, "extends": "eslint:recommended", "parserOptions": {"ecmaVersion": 9, "sourceType": "script"}, "env": {"node": true, "es6": true}, "rules": {"array-callback-return": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-else-return": ["error", {"allowElseIf": false}], "no-lonely-if": "error", "no-var": "error", "object-shorthand": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}], "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-destructuring": ["error", {"object": true, "array": false}], "prefer-spread": "error", "prefer-template": "error", "radix": "error", "semi": "error", "strict": "error", "quotes": ["error", "single"]}}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/paulmillr/readdirp", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "license": "MIT", "main": "index.js", "name": "readdirp", "nyc": {"reporter": ["html", "text"]}, "repository": {"type": "git", "url": "git://github.com/paulmillr/readdirp.git"}, "scripts": {"dtslint": "dtslint", "lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "mocha": "mocha --exit", "nyc": "nyc", "test": "npm run lint && nyc npm run mocha"}, "version": "3.6.0"}