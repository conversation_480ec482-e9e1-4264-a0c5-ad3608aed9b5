{"_from": "html-comment-regex@^1.1.0", "_id": "html-comment-regex@1.1.2", "_inBundle": false, "_integrity": "sha512-P+M65QY2JQ5Y0G9KKdlDpo0zK+/OHptU5AaBwUfAIDJZk1MYf32Frm84EcOytfJE0t5JvkAnKlmjsXDnWzCJmQ==", "_location": "/html-comment-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "html-comment-regex@^1.1.0", "name": "html-comment-regex", "escapedName": "html-comment-regex", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/is-svg"], "_resolved": "https://registry.npmjs.org/html-comment-regex/-/html-comment-regex-1.1.2.tgz", "_shasum": "97d4688aeb5c81886a364faa0cad1dda14d433a7", "_spec": "html-comment-regex@^1.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/is-svg", "author": {"name": "<PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com", "url": "https://github.com/stevemao"}, "bugs": {"url": "https://github.com/stevemao/html-comment-regex/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Regular expression for matching HTML comments", "devDependencies": {"jscs": "^1.11.3", "jshint": "^2.6.3", "mocha": "*"}, "files": ["index.js"], "homepage": "https://github.com/stevemao/html-comment-regex", "keywords": ["html-comment-regex", "text", "string", "regex", "regexp", "re", "match", "test", "find", "pattern", "comment", "comments", "html", "HTML", "HyperText Markup Language"], "license": "MIT", "name": "html-comment-regex", "repository": {"type": "git", "url": "git+https://github.com/stevemao/html-comment-regex.git"}, "scripts": {"lint": "jshint *.js --exclude node_modules && jscs *.js", "test": "npm run-script lint && mocha"}, "version": "1.1.2"}