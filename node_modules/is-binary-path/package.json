{"_from": "is-binary-path@~2.1.0", "_id": "is-binary-path@2.1.0", "_inBundle": false, "_integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==", "_location": "/is-binary-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-binary-path@~2.1.0", "name": "is-binary-path", "escapedName": "is-binary-path", "rawSpec": "~2.1.0", "saveSpec": null, "fetchSpec": "~2.1.0"}, "_requiredBy": ["/chokidar"], "_resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "_shasum": "ea1f7f3b80f064236e83470f86c09c254fb45b09", "_spec": "is-binary-path@~2.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/chokidar", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-binary-path/issues"}, "bundleDependencies": false, "dependencies": {"binary-extensions": "^2.0.0"}, "deprecated": false, "description": "Check if a file path is a binary file", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/is-binary-path#readme", "keywords": ["binary", "extensions", "extension", "file", "path", "check", "detect", "is"], "license": "MIT", "name": "is-binary-path", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-binary-path.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.1.0"}