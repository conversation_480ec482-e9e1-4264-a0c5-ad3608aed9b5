{"_from": "is-resolvable@^1.0.0", "_id": "is-resolvable@1.1.0", "_inBundle": false, "_integrity": "sha512-qgDYXFSR5WvEfuS5dMj6oTMEbrrSaM0CrFk2Yiq/gXnBvD9pMa2jGXxyhGLfvhZpuMZe18CJpFxAt3CRs42NMg==", "_location": "/is-resolvable", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-resolvable@^1.0.0", "name": "is-resolvable", "escapedName": "is-resolvable", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/optimize-css-assets-webpack-plugin/cssnano"], "_resolved": "https://registry.npmjs.org/is-resolvable/-/is-resolvable-1.1.0.tgz", "_shasum": "fb18f87ce1feb925169c9a407c19318a3206ed88", "_spec": "is-resolvable@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/optimize-css-assets-webpack-plugin/node_modules/cssnano", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/shinnn"}, "bugs": {"url": "https://github.com/shinnn/is-resolvable/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if a module ID is resolvable with require()", "devDependencies": {"@shinnn/eslint-config-node": "^5.0.0", "eslint": "^4.16.0", "istanbul": "^0.4.5", "tape": "^4.8.0"}, "eslintConfig": {"extends": "@shinnn/node", "rules": {"no-var": "off", "prefer-template": "off"}}, "files": ["index.js"], "homepage": "https://github.com/shinnn/is-resolvable#readme", "keywords": ["file", "path", "resolve", "resolvable", "check", "module"], "license": "ISC", "name": "is-resolvable", "repository": {"type": "git", "url": "git+https://github.com/shinnn/is-resolvable.git"}, "scripts": {"coverage": "istanbul cover --print=both test.js", "pretest": "eslint --fix --format=codeframe index.js test.js", "test": "node --throw-deprecation --track-heap-objects test.js"}, "version": "1.1.0"}