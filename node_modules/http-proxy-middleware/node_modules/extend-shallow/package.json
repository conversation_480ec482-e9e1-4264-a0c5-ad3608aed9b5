{"_from": "extend-shallow@^3.0.2", "_id": "extend-shallow@3.0.2", "_inBundle": false, "_integrity": "sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q==", "_location": "/http-proxy-middleware/extend-shallow", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "extend-shallow@^3.0.2", "name": "extend-shallow", "escapedName": "extend-shallow", "rawSpec": "^3.0.2", "saveSpec": null, "fetchSpec": "^3.0.2"}, "_requiredBy": ["/http-proxy-middleware/micromatch"], "_resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz", "_shasum": "26a71aaf073b39fb2127172746131c2704028db8", "_spec": "extend-shallow@^3.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/http-proxy-middleware/node_modules/micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/extend-shallow/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}], "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "deprecated": false, "description": "Extend an object with the properties of additional objects. node.js/javascript util.", "devDependencies": {"array-slice": "^1.0.0", "benchmarked": "^2.0.0", "for-own": "^1.0.0", "gulp-format-md": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.1", "minimist": "^1.2.0", "mocha": "^3.5.3", "object-assign": "^4.1.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/extend-shallow", "keywords": ["assign", "clone", "extend", "merge", "obj", "object", "object-assign", "object.assign", "prop", "properties", "property", "props", "shallow", "util", "utility", "utils", "value"], "license": "MIT", "main": "index.js", "name": "extend-shallow", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/extend-shallow.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "related": {"list": ["extend-shallow", "for-in", "for-own", "is-plain-object", "isobject", "kind-of"]}, "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "3.0.2"}