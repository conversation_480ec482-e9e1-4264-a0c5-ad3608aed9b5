{"_from": "is-extendable@^1.0.1", "_id": "is-extendable@1.0.1", "_inBundle": false, "_integrity": "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==", "_location": "/nanomatch/is-extendable", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-extendable@^1.0.1", "name": "is-extendable", "escapedName": "is-extendable", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/nanomatch/extend-shallow"], "_resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz", "_shasum": "a7470f9e426733d81bd81e1155264e3a3507cab4", "_spec": "is-extendable@^1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/nanomatch/node_modules/extend-shallow", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-extendable/issues"}, "bundleDependencies": false, "dependencies": {"is-plain-object": "^2.0.4"}, "deprecated": false, "description": "Returns true if a value is a plain object, array or function.", "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.4.2"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/jonschlinkert/is-extendable", "keywords": ["array", "assign", "check", "date", "extend", "extendable", "extensible", "function", "is", "object", "regex", "test"], "license": "MIT", "main": "index.js", "name": "is-extendable", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-extendable.git"}, "scripts": {"test": "mocha"}, "types": "index.d.ts", "verb": {"related": {"list": ["assign-deep", "is-equal-shallow", "is-plain-object", "isobject", "kind-of"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "1.0.1"}