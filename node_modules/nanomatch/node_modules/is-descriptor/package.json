{"_from": "is-descriptor@^1.0.2", "_id": "is-descriptor@1.0.3", "_inBundle": false, "_integrity": "sha512-JCNNGbwWZEVaSPtS45mdtrneRWJFp07LLmykxeFV5F6oBvNF8vHSfJuJgoT472pSfk+Mf8VnlrspaFBHWM8JAw==", "_location": "/nanomatch/is-descriptor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-descriptor@^1.0.2", "name": "is-descriptor", "escapedName": "is-descriptor", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/nanomatch/define-property"], "_resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.3.tgz", "_shasum": "92d27cb3cd311c4977a4db47df457234a13cb306", "_spec": "is-descriptor@^1.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/nanomatch/node_modules/define-property", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-descriptor/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "deprecated": false, "description": "Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for data descriptors and accessor descriptors.", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.2"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "homepage": "https://github.com/inspect-js/is-descriptor", "keywords": ["accessor", "check", "data", "descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "license": "MIT", "main": "index.js", "name": "is-descriptor", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-descriptor.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.0.3"}