{"_from": "define-property@^2.0.2", "_id": "define-property@2.0.2", "_inBundle": false, "_integrity": "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==", "_location": "/nanomatch/define-property", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "define-property@^2.0.2", "name": "define-property", "escapedName": "define-property", "rawSpec": "^2.0.2", "saveSpec": null, "fetchSpec": "^2.0.2"}, "_requiredBy": ["/nanomatch"], "_resolved": "https://registry.npmjs.org/define-property/-/define-property-2.0.2.tgz", "_shasum": "d459689e8d654ba77e02a817f8710d702cb16e9d", "_spec": "define-property@^2.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/nanomatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/define-property/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "deprecated": false, "description": "Define a non-enumerable property on an object. Uses Reflect.defineProperty when available, otherwise Object.defineProperty.", "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/define-property", "keywords": ["define", "define-property", "enumerable", "key", "non", "non-enumerable", "object", "prop", "property", "value"], "license": "MIT", "main": "index.js", "name": "define-property", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/define-property.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["assign-deep", "extend-shallow", "merge-deep", "mixin-deep"]}, "lint": {"reflinks": true}}, "version": "2.0.2"}