{"_from": "numerify@1.2.9", "_id": "numerify@1.2.9", "_inBundle": false, "_integrity": "sha512-X4QzQiytV5ZN3TVLhzbtFzjTarUNnaa1pgNDFqt7u7Nqhxe7FvY2eYrGt4WYHlYXDqgtfC/n/a5nJ2y0LijV8w==", "_location": "/numerify", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "numerify@1.2.9", "name": "numerify", "escapedName": "numerify", "rawSpec": "1.2.9", "saveSpec": null, "fetchSpec": "1.2.9"}, "_requiredBy": ["/v-charts"], "_resolved": "https://registry.npmjs.org/numerify/-/numerify-1.2.9.tgz", "_shasum": "af4696bb1d57f8d3970a615d8b0cd53d932bd559", "_spec": "numerify@1.2.9", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/v-charts", "author": {"name": "xiguaxigua"}, "bugs": {"url": "https://github.com/xiguaxigua/numerify/issues"}, "bundleDependencies": false, "deprecated": false, "description": "number formatter", "devDependencies": {"babel-core": "^6.26.3", "babel-preset-env": "^1.7.0", "eslint": "^4.19.1", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.12.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "jasmine-core": "^3.1.0", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-phantomjs-launcher": "^1.0.4", "karma-spec-reporter": "0.0.32", "rollup": "^0.59.4", "rollup-plugin-babel": "^3.0.4", "rollup-plugin-commonjs": "^9.1.3", "rollup-plugin-eslint": "^4.0.0", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-uglify": "^4.0.0", "uglify-es": "^3.3.9", "watch": "^1.0.2"}, "homepage": "http://daxigua.me/numerify", "keywords": ["js", "number", "formatter"], "license": "MIT", "main": "lib/index.es.js", "name": "numerify", "repository": {"type": "git", "url": "git+https://github.com/xiguaxigua/numerify.git"}, "scripts": {"build": "rm -f -r lib && node build/build.js", "dev": "node build/watch.js", "prepublishOnly": "npm run build", "test": "karma start "}, "version": "1.2.9"}