{"_args": [["json-parse-even-better-errors@2.3.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "json-parse-even-better-errors@2.3.1", "_id": "json-parse-even-better-errors@2.3.1", "_inBundle": false, "_integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==", "_location": "/json-parse-even-better-errors", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "json-parse-even-better-errors@2.3.1", "name": "json-parse-even-better-errors", "escapedName": "json-parse-even-better-errors", "rawSpec": "2.3.1", "saveSpec": null, "fetchSpec": "2.3.1"}, "_requiredBy": ["/parse-json"], "_resolved": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "_spec": "2.3.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/npm/json-parse-even-better-errors/issues"}, "description": "JSON.parse with context information on error", "devDependencies": {"tap": "^14.6.5"}, "files": ["*.js"], "homepage": "https://github.com/npm/json-parse-even-better-errors#readme", "keywords": ["JSON", "parser"], "license": "MIT", "main": "index.js", "name": "json-parse-even-better-errors", "repository": {"type": "git", "url": "git+https://github.com/npm/json-parse-even-better-errors.git"}, "scripts": {"postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "preversion": "npm t", "snap": "tap", "test": "tap"}, "tap": {"check-coverage": true}, "version": "2.3.1"}