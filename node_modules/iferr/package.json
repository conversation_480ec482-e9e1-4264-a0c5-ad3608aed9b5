{"_from": "iferr@^0.1.5", "_id": "iferr@0.1.5", "_inBundle": false, "_integrity": "sha512-DUNFN5j7Tln0D+TxzloUjKB+CtVu6myn0JEFak6dG18mNt9YkQ6lzGCdafwofISZ1lLF3xRHJ98VKy9ynkcFaA==", "_location": "/iferr", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "iferr@^0.1.5", "name": "iferr", "escapedName": "iferr", "rawSpec": "^0.1.5", "saveSpec": null, "fetchSpec": "^0.1.5"}, "_requiredBy": ["/copy-concurrently", "/fs-write-stream-atomic"], "_resolved": "https://registry.npmjs.org/iferr/-/iferr-0.1.5.tgz", "_shasum": "c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501", "_spec": "iferr@^0.1.5", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/copy-concurrently", "author": {"name": "Nadav I<PERSON>gi"}, "bugs": {"url": "https://github.com/shesek/iferr/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Higher-order functions for easier error handling", "devDependencies": {"coffee-script": "^1.7.1", "mocha": "^1.18.2"}, "homepage": "https://github.com/shesek/iferr", "keywords": ["error", "errors"], "license": "MIT", "main": "index.js", "name": "iferr", "repository": {"type": "git", "url": "git+https://github.com/shesek/iferr.git"}, "scripts": {"prepublish": "coffee -c index.coffee", "test": "mocha"}, "version": "0.1.5"}