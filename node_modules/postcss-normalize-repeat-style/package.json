{"_args": [["postcss-normalize-repeat-style@4.0.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "postcss-normalize-repeat-style@4.0.2", "_id": "postcss-normalize-repeat-style@4.0.2", "_inBundle": false, "_integrity": "sha512-qvigdYYMpSuoFs3Is/f5nHdRLJN/ITA7huIoCyqqENJe9PvPmLhNLMu7QTjPdtnVf6OcYYO5SHonx4+fbJE1+Q==", "_location": "/postcss-normalize-repeat-style", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "postcss-normalize-repeat-style@4.0.2", "name": "postcss-normalize-repeat-style", "escapedName": "postcss-normalize-repeat-style", "rawSpec": "4.0.2", "saveSpec": null, "fetchSpec": "4.0.2"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "https://registry.npmjs.org/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-4.0.2.tgz", "_spec": "4.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "dependencies": {"cssnano-util-get-arguments": "^4.0.0", "cssnano-util-get-match": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "description": "Convert two value syntax for repeat-style into one value.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["LICENSE-MIT", "dist"], "homepage": "https://github.com/cssnano/cssnano", "license": "MIT", "main": "dist/index.js", "name": "postcss-normalize-repeat-style", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.2"}