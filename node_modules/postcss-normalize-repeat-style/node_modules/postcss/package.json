{"_args": [["postcss@7.0.39", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "postcss@7.0.39", "_id": "postcss@7.0.39", "_inBundle": false, "_integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "_location": "/postcss-normalize-repeat-style/postcss", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "postcss@7.0.39", "name": "postcss", "escapedName": "postcss", "rawSpec": "7.0.39", "saveSpec": null, "fetchSpec": "7.0.39"}, "_requiredBy": ["/postcss-normalize-repeat-style"], "_resolved": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "_spec": "7.0.39", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": {"./lib/terminal-highlight": false, "fs": false}, "bugs": {"url": "https://github.com/postcss/postcss/issues"}, "dependencies": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}, "description": "Tool for transforming styles with JS plugins", "engines": {"node": ">=6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "homepage": "https://postcss.org/", "keywords": ["css", "postcss", "rework", "preprocessor", "parser", "source map", "transform", "manipulation", "transpiler"], "license": "MIT", "main": "lib/postcss", "name": "postcss", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss.git"}, "types": "lib/postcss.d.ts", "version": "7.0.39"}