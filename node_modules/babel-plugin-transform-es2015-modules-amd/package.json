{"_args": [["babel-plugin-transform-es2015-modules-amd@6.24.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "babel-plugin-transform-es2015-modules-amd@6.24.1", "_id": "babel-plugin-transform-es2015-modules-amd@6.24.1", "_inBundle": false, "_integrity": "sha512-LnIIdGWIKdw7zwckqx+eGjcS8/cl8D74A3BpJbGjKTFFNJSMrjN4bIh22HY1AlkUbeLG6X6OZj56BDvWD+OeFA==", "_location": "/babel-plugin-transform-es2015-modules-amd", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-plugin-transform-es2015-modules-amd@6.24.1", "name": "babel-plugin-transform-es2015-modules-amd", "escapedName": "babel-plugin-transform-es2015-modules-amd", "rawSpec": "6.24.1", "saveSpec": null, "fetchSpec": "6.24.1"}, "_requiredBy": ["/babel-plugin-transform-es2015-modules-umd", "/babel-preset-env"], "_resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz", "_spec": "6.24.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "dependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}, "description": "This plugin transforms ES2015 modules to AMD", "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "babel-plugin-transform-es2015-modules-amd", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-es2015-modules-amd"}, "version": "6.24.1"}