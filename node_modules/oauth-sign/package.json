{"_from": "oauth-sign@~0.9.0", "_id": "oauth-sign@0.9.0", "_inBundle": false, "_integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==", "_location": "/oauth-sign", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "oauth-sign@~0.9.0", "name": "oauth-sign", "escapedName": "oauth-sign", "rawSpec": "~0.9.0", "saveSpec": null, "fetchSpec": "~0.9.0"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz", "_shasum": "47a7b016baa68b5fa0ecf3dee08a85c679ac6455", "_spec": "oauth-sign@~0.9.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/request", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "bugs": {"url": "https://github.com/mikeal/oauth-sign/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "devDependencies": {}, "engines": {"node": "*"}, "files": ["index.js"], "homepage": "https://github.com/mikeal/oauth-sign#readme", "license": "Apache-2.0", "main": "index.js", "name": "oauth-sign", "optionalDependencies": {}, "repository": {"url": "git+https://github.com/mikeal/oauth-sign.git"}, "scripts": {"test": "node test.js"}, "version": "0.9.0"}