{"_args": [["has@1.0.4", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "has@1.0.4", "_id": "has@1.0.4", "_inBundle": false, "_integrity": "sha512-qdSAmqLF6209RFj4VVItywPMbm3vWylknmB3nvNiUIs72xAimcM8nVYxYr7ncvZq5qzk9MKIZR8ijqD/1QuYjQ==", "_location": "/has", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "has@1.0.4", "name": "has", "escapedName": "has", "rawSpec": "1.0.4", "saveSpec": null, "fetchSpec": "1.0.4"}, "_requiredBy": ["/colormin", "/cssnano", "/cssnano-preset-default/postcss-colormin", "/cssnano-preset-default/postcss-minify-selectors", "/cssnano-preset-default/postcss-reduce-initial", "/cssnano-preset-default/postcss-reduce-transforms", "/postcss-merge-idents", "/postcss-minify-selectors", "/postcss-normalize-positions", "/postcss-normalize-string", "/postcss-reduce-transforms", "/postcss-zindex"], "_resolved": "https://registry.npmjs.org/has/-/has-1.0.4.tgz", "_spec": "1.0.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tarruda/has/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Object.prototype.hasOwnProperty.call shortcut", "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "eslint": "^4.19.1", "tape": "^4.9.0"}, "engines": {"node": ">= 0.4.0"}, "homepage": "https://github.com/tarruda/has", "license": "MIT", "licenses": [{"type": "MIT", "url": "https://github.com/tarruda/has/blob/master/LICENSE-MIT"}], "main": "./src", "name": "has", "repository": {"type": "git", "url": "git://github.com/tarruda/has.git"}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "tape test"}, "version": "1.0.4"}