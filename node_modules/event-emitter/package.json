{"_args": [["event-emitter@0.3.5", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "event-emitter@0.3.5", "_id": "event-emitter@0.3.5", "_inBundle": false, "_integrity": "sha512-D9rRn9y7kLPnJ+hMq7S/nhvoKwwvVJahBi2BPmx3bvbsEdK3W9ii8cBSGjP+72/LnM4n6fo3+dkCX5FeTQruXA==", "_location": "/event-emitter", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "event-emitter@0.3.5", "name": "event-emitter", "escapedName": "event-emitter", "rawSpec": "0.3.5", "saveSpec": null, "fetchSpec": "0.3.5"}, "_requiredBy": ["/es6-map", "/es6-set", "/esniff"], "_resolved": "https://registry.npmjs.org/event-emitter/-/event-emitter-0.3.5.tgz", "_spec": "0.3.5", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.medikoo.com/"}, "bugs": {"url": "https://github.com/medikoo/event-emitter/issues"}, "dependencies": {"d": "1", "es5-ext": "~0.10.14"}, "description": "Environment agnostic event emitter", "devDependencies": {"tad": "~0.2.7", "xlint": "~0.2.2", "xlint-jslint-medikoo": "~0.1.4"}, "homepage": "https://github.com/medikoo/event-emitter#readme", "keywords": ["event", "events", "trigger", "observer", "listener", "emitter", "pubsub"], "license": "MIT", "name": "event-emitter", "repository": {"type": "git", "url": "git://github.com/medikoo/event-emitter.git"}, "scripts": {"lint": "node node_modules/xlint/bin/xlint --linter=node_modules/xlint-jslint-medikoo/index.js --no-cache --no-stream", "lint-console": "node node_modules/xlint/bin/xlint --linter=node_modules/xlint-jslint-medikoo/index.js --watch", "test": "node ./node_modules/tad/bin/tad"}, "version": "0.3.5"}