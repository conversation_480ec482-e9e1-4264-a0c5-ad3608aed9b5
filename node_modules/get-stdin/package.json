{"_from": "get-stdin@^4.0.1", "_id": "get-stdin@4.0.1", "_inBundle": false, "_integrity": "sha512-F5aQMywwJ2n85s4hJPTT9RPxGmubonuB10MNYo17/xph174n2MIR33HRguhzVag10O/npM7SPk73LMZNP+FaWw==", "_location": "/get-stdin", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "get-stdin@^4.0.1", "name": "get-stdin", "escapedName": "get-stdin", "rawSpec": "^4.0.1", "saveSpec": null, "fetchSpec": "^4.0.1"}, "_requiredBy": ["/internal-ip/strip-indent", "/node-sass"], "_resolved": "https://registry.npmjs.org/get-stdin/-/get-stdin-4.0.1.tgz", "_shasum": "b968c6b0a04384324902e8bf1a5df32579a450fe", "_spec": "get-stdin@^4.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/node-sass", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/get-stdin/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Easier stdin", "devDependencies": {"ava": "0.0.4", "buffer-equal": "0.0.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/get-stdin#readme", "keywords": ["std", "stdin", "stdio", "concat", "buffer", "stream", "process", "stream"], "license": "MIT", "name": "get-stdin", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stdin.git"}, "scripts": {"test": "node test.js && node test-buffer.js && echo unicorns | node test-real.js"}, "version": "4.0.1"}