{"_from": "es-array-method-boxes-properly@^1.0.0", "_id": "es-array-method-boxes-properly@1.0.0", "_inBundle": false, "_integrity": "sha512-wd6JXUmyHmt8T5a2xreUwKcGPq6f1f+WwIJkijUqiGcJz1qqnZgP6XIK+QyIWU5lT7imeNxUll48bziG+TSYcA==", "_location": "/es-array-method-boxes-properly", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "es-array-method-boxes-properly@^1.0.0", "name": "es-array-method-boxes-properly", "escapedName": "es-array-method-boxes-properly", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/array.prototype.reduce"], "_resolved": "https://registry.npmjs.org/es-array-method-boxes-properly/-/es-array-method-boxes-properly-1.0.0.tgz", "_shasum": "873f3e84418de4ee19c5be752990b2e44718d09e", "_spec": "es-array-method-boxes-properly@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/array.prototype.reduce", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ljharb/es-array-method-boxes-properly/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Utility package to determine if an `Array.prototype` method properly boxes the callback's receiver and third argument.", "devDependencies": {"@ljharb/eslint-config": "^14.1.0", "eslint": "^6.4.0", "safe-publish-latest": "^1.1.3", "tape": "^4.11.0"}, "homepage": "https://github.com/ljharb/es-array-method-boxes-properly#readme", "license": "MIT", "main": "index.js", "name": "es-array-method-boxes-properly", "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-array-method-boxes-properly.git"}, "scripts": {"lint": "eslint .", "posttest": "npx aud", "prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "node test"}, "version": "1.0.0"}