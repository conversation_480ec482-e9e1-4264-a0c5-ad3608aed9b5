{"_from": "is-plain-object@^2.0.4", "_id": "is-plain-object@2.0.4", "_inBundle": false, "_integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "_location": "/is-plain-object", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-plain-object@^2.0.4", "name": "is-plain-object", "escapedName": "is-plain-object", "rawSpec": "^2.0.4", "saveSpec": null, "fetchSpec": "^2.0.4"}, "_requiredBy": ["/clone-deep", "/http-proxy-middleware/is-extendable", "/mixin-deep/is-extendable", "/nanomatch/is-extendable", "/regex-not/is-extendable", "/set-value", "/split-string/is-extendable", "/to-regex/is-extendable", "/watchpack-chokidar2/is-extendable", "/webpack-dev-server/is-extendable"], "_resolved": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "_shasum": "2c163b3fafb1b606d9d17928f05c2a1c38e07677", "_spec": "is-plain-object@^2.0.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "http://onokumus.com"}, {"name": "<PERSON>", "url": "https://svachon.com"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "dependencies": {"isobject": "^3.0.1"}, "deprecated": false, "description": "Returns true if an object was created by the `Object` constructor.", "devDependencies": {"browserify": "^14.4.0", "chai": "^4.0.2", "gulp-format-md": "^1.0.0", "mocha": "^3.4.2", "mocha-phantomjs": "^4.1.0", "phantomjs": "^2.1.7", "uglify-js": "^3.0.24"}, "engines": {"node": ">=0.10.0"}, "files": ["index.d.ts", "index.js"], "homepage": "https://github.com/jonschlinkert/is-plain-object", "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "license": "MIT", "main": "index.js", "name": "is-plain-object", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-plain-object.git"}, "scripts": {"browserify": "browserify index.js --standalone isPlainObject | uglifyjs --compress --mangle -o browser/is-plain-object.js", "test": "npm run test_node && npm run browserify && npm run test_browser", "test_browser": "mocha-phantomjs test/browser.html", "test_node": "mocha"}, "types": "index.d.ts", "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}, "version": "2.0.4"}