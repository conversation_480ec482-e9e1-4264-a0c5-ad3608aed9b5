{"_from": "callsites@^2.0.0", "_id": "callsites@2.0.0", "_inBundle": false, "_integrity": "sha512-ksWePWBloaWPxJYQ8TL0JHvtci6G5QTKwQ95RcWAa/lzoAKuAOflGdAK92hpHXjkwb8zLxoLNUoNYZgVsaJzvQ==", "_location": "/callsites", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "callsites@^2.0.0", "name": "callsites", "escapedName": "callsites", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/caller-callsite"], "_resolved": "https://registry.npmjs.org/callsites/-/callsites-2.0.0.tgz", "_shasum": "06eb84f00eea413da86affefacbffb36093b3c50", "_spec": "callsites@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/caller-callsite", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/callsites/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Get callsites from the V8 stack trace API", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/callsites#readme", "keywords": ["stacktrace", "v8", "callsite", "callsites", "stack", "trace", "function", "file", "line", "debug"], "license": "MIT", "name": "callsites", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/callsites.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.0", "xo": {"esnext": true}}