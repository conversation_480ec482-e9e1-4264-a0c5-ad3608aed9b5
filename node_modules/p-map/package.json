{"_from": "p-map@^4.0.0", "_id": "p-map@4.0.0", "_inBundle": false, "_integrity": "sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==", "_location": "/p-map", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "p-map@^4.0.0", "name": "p-map", "escapedName": "p-map", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/cacache"], "_resolved": "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz", "_shasum": "bb2f95a5eda2ec168ec9274e06a747c3e2904d2b", "_spec": "p-map@^4.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/cacache", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-map/issues"}, "bundleDependencies": false, "dependencies": {"aggregate-error": "^3.0.0"}, "deprecated": false, "description": "Map over promises concurrently", "devDependencies": {"ava": "^2.2.0", "delay": "^4.1.0", "in-range": "^2.0.0", "random-int": "^2.0.0", "time-span": "^3.1.0", "tsd": "^0.7.4", "xo": "^0.27.2"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/p-map#readme", "keywords": ["promise", "map", "resolved", "wait", "collection", "iterable", "iterator", "race", "fulfilled", "async", "await", "promises", "concurrently", "concurrency", "parallel", "bluebird"], "license": "MIT", "name": "p-map", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-map.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.0.0"}