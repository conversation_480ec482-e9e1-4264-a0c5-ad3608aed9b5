{"_from": "hash.js@^1.0.0", "_id": "hash.js@1.1.7", "_inBundle": false, "_integrity": "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==", "_location": "/hash.js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "hash.js@^1.0.0", "name": "hash.js", "escapedName": "hash.js", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/elliptic", "/hmac-drbg"], "_resolved": "https://registry.npmjs.org/hash.js/-/hash.js-1.1.7.tgz", "_shasum": "0babca538e8d4ee4a0f8988d68866537a003cf42", "_spec": "hash.js@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/elliptic", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/indutny/hash.js/issues"}, "bundleDependencies": false, "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}, "deprecated": false, "description": "Various hash functions that could be run by both browser and node", "devDependencies": {"eslint": "^4.19.1", "mocha": "^5.2.0"}, "homepage": "https://github.com/indutny/hash.js", "keywords": ["hash", "sha256", "sha224", "hmac"], "license": "MIT", "main": "lib/hash.js", "name": "hash.js", "repository": {"type": "git", "url": "git+ssh://**************/indutny/hash.js.git"}, "scripts": {"lint": "eslint lib/*.js lib/**/*.js lib/**/**/*.js test/*.js", "test": "mocha --reporter=spec test/*-test.js && npm run lint"}, "typings": "lib/hash.d.ts", "version": "1.1.7"}