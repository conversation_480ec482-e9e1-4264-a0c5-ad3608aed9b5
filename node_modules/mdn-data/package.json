{"_from": "mdn-data@2.0.4", "_id": "mdn-data@2.0.4", "_inBundle": false, "_integrity": "sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA==", "_location": "/mdn-data", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "mdn-data@2.0.4", "name": "mdn-data", "escapedName": "mdn-data", "rawSpec": "2.0.4", "saveSpec": null, "fetchSpec": "2.0.4"}, "_requiredBy": ["/css-tree"], "_resolved": "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.4.tgz", "_shasum": "699b3c38ac6f1d728091a64650b65d388502fd5b", "_spec": "mdn-data@2.0.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/css-tree", "author": {"name": "Mozilla Developer Network"}, "bugs": {"url": "https://github.com/mdn/data/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Open Web data by the Mozilla Developer Network", "devDependencies": {"ajv": "^5.0.1", "better-ajv-errors": "^0.5.1"}, "files": ["api/*.json", "css/*.json", "l10n/*.json"], "homepage": "https://developer.mozilla.org", "keywords": ["data", "mdn", "mozilla", "css"], "license": "CC0-1.0", "main": "index.js", "name": "mdn-data", "repository": {"type": "git", "url": "git+https://github.com/mdn/data.git"}, "scripts": {"lint": "node test/lint", "test": "npm run lint", "travis": "npm test"}, "version": "2.0.4"}