{"_from": "has-flag@^4.0.0", "_id": "has-flag@4.0.0", "_inBundle": false, "_integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "_location": "/has-flag", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "has-flag@^4.0.0", "name": "has-flag", "escapedName": "has-flag", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/supports-color"], "_resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "_shasum": "944771fd9c81c81265c4d6941860da06bb59479b", "_spec": "has-flag@^4.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/supports-color", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/has-flag/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if argv has a specific flag", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/has-flag#readme", "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "license": "MIT", "name": "has-flag", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/has-flag.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.0.0"}