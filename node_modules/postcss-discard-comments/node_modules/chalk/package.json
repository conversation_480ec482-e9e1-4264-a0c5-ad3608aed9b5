{"_args": [["chalk@1.1.3", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "chalk@1.1.3", "_id": "chalk@1.1.3", "_inBundle": false, "_integrity": "sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==", "_location": "/postcss-discard-comments/chalk", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "chalk@1.1.3", "name": "chalk", "escapedName": "chalk", "rawSpec": "1.1.3", "saveSpec": null, "fetchSpec": "1.1.3"}, "_requiredBy": ["/postcss-discard-comments/postcss"], "_resolved": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "_spec": "1.1.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "description": "Terminal string styling done right. Much color.", "devDependencies": {"coveralls": "^2.11.2", "matcha": "^0.6.0", "mocha": "*", "nyc": "^3.0.0", "require-uncached": "^1.0.2", "resolve-from": "^1.0.0", "semver": "^4.3.3", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/chalk/chalk#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/qix-"}], "name": "chalk", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "scripts": {"bench": "matcha benchmark.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "test": "xo && mocha"}, "version": "1.1.3", "xo": {"envs": ["node", "mocha"]}}