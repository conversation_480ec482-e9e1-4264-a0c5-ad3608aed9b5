{"_args": [["has-flag@1.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "has-flag@1.0.0", "_id": "has-flag@1.0.0", "_inBundle": false, "_integrity": "sha512-DyYHfIYwAJmjAjSSPKANxI8bFY9YtFrgkAfinBojQ8YJTOuOuav64tMUJv584SES4xl74PmuaevIyaLESHdTAA==", "_location": "/postcss-discard-comments/has-flag", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "has-flag@1.0.0", "name": "has-flag", "escapedName": "has-flag", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/postcss-discard-comments/supports-color"], "_resolved": "https://registry.npmjs.org/has-flag/-/has-flag-1.0.0.tgz", "_spec": "1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/has-flag/issues"}, "description": "Check if argv has a specific flag", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/has-flag#readme", "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/qix-"}], "name": "has-flag", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/has-flag.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.0"}