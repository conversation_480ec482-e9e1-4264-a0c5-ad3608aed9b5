{"_args": [["postcss-discard-comments@2.0.4", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "postcss-discard-comments@2.0.4", "_id": "postcss-discard-comments@2.0.4", "_inBundle": false, "_integrity": "sha512-yGbyBDo5FxsImE90LD8C87vgnNlweQkODMkUZlDVM/CBgLr9C5RasLGJxxh9GjVOBeG8NcCMatoqI1pXg8JNXg==", "_location": "/postcss-discard-comments", "_phantomChildren": {"escape-string-regexp": "1.0.5", "has-ansi": "2.0.0", "js-base64": "2.6.4", "strip-ansi": "3.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "postcss-discard-comments@2.0.4", "name": "postcss-discard-comments", "escapedName": "postcss-discard-comments", "rawSpec": "2.0.4", "saveSpec": null, "fetchSpec": "2.0.4"}, "_requiredBy": ["/cssnano"], "_resolved": "https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-2.0.4.tgz", "_spec": "2.0.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "ava": {"require": "babel-core/register"}, "bugs": {"url": "https://github.com/ben-eb/postcss-discard-comments/issues"}, "dependencies": {"postcss": "^5.0.14"}, "description": "Discard comments in your CSS files with PostCSS.", "devDependencies": {"ava": "^0.11.0", "babel-cli": "^6.5.1", "babel-core": "^6.5.1", "babel-plugin-add-module-exports": "^0.1.2", "babel-preset-es2015": "^6.5.0", "babel-preset-es2015-loose": "^7.0.0", "babel-preset-stage-0": "^6.5.0", "del-cli": "^0.2.0", "eslint": "^1.10.3", "eslint-config-cssnano": "^1.0.0", "postcss-scss": "^0.1.3", "postcss-simple-vars": "^1.2.0"}, "eslintConfig": {"extends": "cssnano"}, "files": ["dist", "LICENSE-MIT"], "homepage": "https://github.com/ben-eb/postcss-discard-comments", "keywords": ["css", "comments", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-discard-comments", "repository": {"type": "git", "url": "git+https://github.com/ben-eb/postcss-discard-comments.git"}, "scripts": {"prepublish": "del-cli dist && BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/", "pretest": "eslint src", "test": "ava src/__tests__"}, "version": "2.0.4"}