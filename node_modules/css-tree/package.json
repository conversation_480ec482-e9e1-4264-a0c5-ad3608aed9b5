{"_args": [["css-tree@1.0.0-alpha.37", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "css-tree@1.0.0-alpha.37", "_id": "css-tree@1.0.0-alpha.37", "_inBundle": false, "_integrity": "sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg==", "_location": "/css-tree", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "css-tree@1.0.0-alpha.37", "name": "css-tree", "escapedName": "css-tree", "rawSpec": "1.0.0-alpha.37", "saveSpec": null, "fetchSpec": "1.0.0-alpha.37"}, "_requiredBy": ["/cssnano-preset-default/svgo"], "_resolved": "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.37.tgz", "_spec": "1.0.0-alpha.37", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com", "url": "https://github.com/lahmatiy"}, "browser": {"./data": "./dist/default-syntax.json"}, "bugs": {"url": "https://github.com/csstree/csstree/issues"}, "dependencies": {"mdn-data": "2.0.4", "source-map": "^0.6.1"}, "description": "CSSTree is a tool set to work with CSS, including fast detailed parser (string->AST), walker (AST traversal), generator (AST->string) and lexer (validation and matching) based on knowledge of spec and browser implementations", "devDependencies": {"coveralls": "^3.0.4", "eslint": "^6.3.0", "json-to-ast": "^2.1.0", "mocha": "^5.2.0", "nyc": "^14.1.1", "rollup": "^1.22.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.2.0", "terser": "^4.3.4"}, "engines": {"node": ">=8.0.0"}, "files": ["data", "dist", "lib"], "homepage": "https://github.com/csstree/csstree#readme", "keywords": ["css", "ast", "tokenizer", "parser", "walker", "lexer", "generator", "utils", "syntax", "validation"], "license": "MIT", "main": "./lib/index", "name": "css-tree", "repository": {"type": "git", "url": "git+https://github.com/csstree/csstree.git"}, "scripts": {"build": "npm run gen:syntax && rollup --config && terser dist/csstree.js --compress --mangle -o dist/csstree.min.js", "coverage": "nyc npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "gen:syntax": "node scripts/gen-syntax-data", "hydrogen": "node --trace-hydrogen --trace-phase=Z --trace-deopt --code-comments --hydrogen-track-positions --redirect-code-traces --redirect-code-traces-to=code.asm --trace_hydrogen_file=code.cfg --print-opt-code bin/parse --stat -o /dev/null", "lint": "eslint data lib scripts test && node scripts/review-syntax-patch --lint && node scripts/update-docs --lint", "lint-and-test": "npm run lint && npm test", "prepublishOnly": "npm run build", "review:syntax-patch": "node scripts/review-syntax-patch", "test": "mocha --reporter progress", "travis": "nyc npm run lint-and-test && npm run coveralls", "update:docs": "node scripts/update-docs"}, "version": "1.0.0-alpha.37"}