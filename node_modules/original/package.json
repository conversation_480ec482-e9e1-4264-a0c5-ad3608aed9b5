{"_args": [["original@1.0.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "original@1.0.2", "_id": "original@1.0.2", "_inBundle": false, "_integrity": "sha512-hyBVl6iqqUOJ8FqRe+l/gS8H+kKYjrEndd5Pm1MfBtsEKA038HkkdbAl/72EAXGyonD/PFsvmVG+EvcIpliMBg==", "_location": "/original", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "original@1.0.2", "name": "original", "escapedName": "original", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/eventsource"], "_resolved": "https://registry.npmjs.org/original/-/original-1.0.2.tgz", "_spec": "1.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/unshiftio/original/issues"}, "dependencies": {"url-parse": "^1.4.3"}, "description": "Generate the origin from an URL or check if two URL/Origins are the same", "devDependencies": {"assume": "~2.1.0", "istanbul": "0.4.x", "mocha": "~3.5.0", "pre-commit": "~1.2.0"}, "homepage": "https://github.com/unshiftio/original#readme", "keywords": ["origin", "url", "parse"], "license": "MIT", "main": "index.js", "name": "original", "repository": {"type": "git", "url": "git+https://github.com/unshiftio/original.git"}, "scripts": {"100%": "istanbul check-coverage --statements 100 --functions 100 --lines 100 --branches 100", "coverage": "istanbul cover _mocha -- test.js", "test": "mocha test.js", "test-travis": "istanbul cover _mocha --report lcovonly -- test.js", "watch": "mocha --watch test.js"}, "version": "1.0.2"}