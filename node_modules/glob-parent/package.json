{"_from": "glob-parent@~5.1.2", "_id": "glob-parent@5.1.2", "_inBundle": false, "_integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "_location": "/glob-parent", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "glob-parent@~5.1.2", "name": "glob-parent", "escapedName": "glob-parent", "rawSpec": "~5.1.2", "saveSpec": null, "fetchSpec": "~5.1.2"}, "_requiredBy": ["/chokidar"], "_resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "_shasum": "869832c58034fe68a4093c17dc15e8340d8401c4", "_spec": "glob-parent@~5.1.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/chokidar", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "bugs": {"url": "https://github.com/gulpjs/glob-parent/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"is-glob": "^4.0.1"}, "deprecated": false, "description": "Extract the non-magic parent path from a glob string.", "devDependencies": {"coveralls": "^3.0.11", "eslint": "^2.13.1", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^6.0.2", "nyc": "^13.3.0"}, "engines": {"node": ">= 6"}, "files": ["LICENSE", "index.js"], "homepage": "https://github.com/gulpjs/glob-parent#readme", "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "license": "ISC", "main": "index.js", "name": "glob-parent", "repository": {"type": "git", "url": "git+https://github.com/gulpjs/glob-parent.git"}, "scripts": {"azure-pipelines": "nyc mocha --async-only --reporter xunit -O output=test.xunit", "coveralls": "nyc report --reporter=text-lcov | coveralls", "lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "version": "5.1.2"}