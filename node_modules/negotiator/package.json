{"_from": "negotiator@^0.6.2", "_id": "negotiator@0.6.4", "_inBundle": false, "_integrity": "sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w==", "_location": "/negotiator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "negotiator@^0.6.2", "name": "negotiator", "escapedName": "negotiator", "rawSpec": "^0.6.2", "saveSpec": null, "fetchSpec": "^0.6.2"}, "_requiredBy": ["/compression", "/make-fetch-happen"], "_resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.4.tgz", "_shasum": "777948e2452651c570b712dd01c23e262713fff7", "_spec": "negotiator@^0.6.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/make-fetch-happen", "bugs": {"url": "https://github.com/jshttp/negotiator/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}], "deprecated": false, "description": "HTTP content negotiation", "devDependencies": {"eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "mocha": "9.1.3", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["lib/", "HISTORY.md", "LICENSE", "index.js", "README.md"], "homepage": "https://github.com/jshttp/negotiator#readme", "keywords": ["http", "content negotiation", "accept", "accept-language", "accept-encoding", "accept-charset"], "license": "MIT", "name": "negotiator", "repository": {"type": "git", "url": "git+https://github.com/jshttp/negotiator.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "0.6.4"}