{"_args": [["node-forge@0.10.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "node-forge@0.10.0", "_id": "node-forge@0.10.0", "_inBundle": false, "_integrity": "sha512-PP<PERSON>8eEeG9saEUvI97fm4OYxXVB6bFvyNTyiUOBichBpFG8A1Ljw3bY62+5oOjDEMHRnd0Y7HQ+x7uzxOzC6JA==", "_location": "/node-forge", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "node-forge@0.10.0", "name": "node-forge", "escapedName": "node-forge", "rawSpec": "0.10.0", "saveSpec": null, "fetchSpec": "0.10.0"}, "_requiredBy": ["/selfsigned"], "_resolved": "https://registry.npmjs.org/node-forge/-/node-forge-0.10.0.tgz", "_spec": "0.10.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "Digital Bazaar, Inc.", "email": "<EMAIL>", "url": "http://digitalbazaar.com/"}, "browser": {"buffer": false, "crypto": false, "process": false}, "bugs": {"url": "https://github.com/digitalbazaar/forge/issues", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "JavaScript implementations of network transports, cryptography, ciphers, PKI, message digests, and various utilities.", "devDependencies": {"browserify": "^16.5.2", "commander": "^2.20.0", "cross-env": "^5.2.1", "eslint": "^7.8.1", "eslint-config-digitalbazaar": "^2.5.0", "express": "^4.16.2", "karma": "^4.4.1", "karma-browserify": "^7.0.0", "karma-chrome-launcher": "^3.1.0", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^1.3.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.5", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^2.0.2", "karma-sourcemap-loader": "^0.3.8", "karma-tap-reporter": "0.0.6", "karma-webpack": "^4.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "nodejs-websocket": "^1.7.1", "nyc": "^15.1.0", "opts": "^1.2.7", "webpack": "^4.44.1", "webpack-cli": "^3.3.12", "worker-loader": "^2.0.0"}, "engines": {"node": ">= 6.0.0"}, "files": ["lib/*.js", "flash/swf/*.swf", "dist/*.min.js", "dist/*.min.js.map"], "homepage": "https://github.com/digitalbazaar/forge", "jspm": {"format": "amd"}, "keywords": ["aes", "asn", "asn.1", "cbc", "crypto", "cryptography", "csr", "des", "gcm", "hmac", "http", "https", "md5", "network", "pkcs", "pki", "prng", "rc2", "rsa", "sha1", "sha256", "sha384", "sha512", "ssh", "tls", "x.509", "x509"], "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "main": "lib/index.js", "name": "node-forge", "nyc": {"exclude": ["tests"]}, "repository": {"type": "git", "url": "git+https://github.com/digitalbazaar/forge.git"}, "scripts": {"build": "webpack", "coverage": "rm -rf coverage && nyc --reporter=lcov --reporter=text-summary npm test", "coverage-report": "nyc report", "lint": "eslint *.js lib/*.js tests/*.js tests/**/*.js examples/*.js flash/*.js", "prepublish": "npm run build", "test": "cross-env NODE_ENV=test mocha -t 30000 -R ${REPORTER:-spec} tests/unit/index.js", "test-build": "webpack --config webpack-tests.config.js", "test-karma": "karma start", "test-karma-sauce": "karma start karma-sauce.conf", "test-server": "node tests/server.js", "test-server-webid": "node tests/websockets/server-webid.js", "test-server-ws": "node tests/websockets/server-ws.js"}, "version": "0.10.0"}