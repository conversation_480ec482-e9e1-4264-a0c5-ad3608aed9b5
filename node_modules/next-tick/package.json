{"_args": [["next-tick@1.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "next-tick@1.0.0", "_id": "next-tick@1.0.0", "_inBundle": false, "_integrity": "sha1-yobR/ogoFpsBICCOPchCS524NCw=", "_location": "/next-tick", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "next-tick@1.0.0", "name": "next-tick", "escapedName": "next-tick", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/es5-ext"], "_resolved": "https://registry.npmjs.org/next-tick/-/next-tick-1.0.0.tgz", "_spec": "1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.medikoo.com/"}, "bugs": {"url": "https://github.com/medikoo/next-tick/issues"}, "description": "Environment agnostic nextTick polyfill", "devDependencies": {"tad": "^0.2.4", "xlint": "^0.2.2", "xlint-jslint-medikoo": "^0.1.4"}, "homepage": "https://github.com/medikoo/next-tick#readme", "keywords": ["nexttick", "setImmediate", "setTimeout", "async"], "license": "MIT", "name": "next-tick", "repository": {"type": "git", "url": "git://github.com/medikoo/next-tick.git"}, "scripts": {"lint": "node node_modules/xlint/bin/xlint --linter=node_modules/xlint-jslint-medikoo/index.js --no-cache --no-stream", "lint-console": "node node_modules/xlint/bin/xlint --linter=node_modules/xlint-jslint-medikoo/index.js --watch", "test": "node node_modules/tad/bin/tad"}, "version": "1.0.0"}