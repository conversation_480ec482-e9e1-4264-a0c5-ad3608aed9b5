{"_from": "minipass-sized@^1.0.3", "_id": "minipass-sized@1.0.3", "_inBundle": false, "_integrity": "sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==", "_location": "/minipass-sized", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "minipass-sized@^1.0.3", "name": "minipass-sized", "escapedName": "minipass-sized", "rawSpec": "^1.0.3", "saveSpec": null, "fetchSpec": "^1.0.3"}, "_requiredBy": ["/minipass-fetch"], "_resolved": "https://registry.npmjs.org/minipass-sized/-/minipass-sized-1.0.3.tgz", "_shasum": "70ee5a7c5052070afacfbc22977ea79def353b70", "_spec": "minipass-sized@^1.0.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/minipass-fetch", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me"}, "bugs": {"url": "https://github.com/isaacs/minipass-sized/issues"}, "bundleDependencies": false, "dependencies": {"minipass": "^3.0.0"}, "deprecated": false, "description": "A Minipass stream that raises an error if you get a different number of bytes than expected", "devDependencies": {"tap": "^14.6.4"}, "directories": {"test": "test"}, "engines": {"node": ">=8"}, "homepage": "https://github.com/isaacs/minipass-sized#readme", "keywords": ["minipass", "size", "length"], "license": "ISC", "main": "index.js", "name": "minipass-sized", "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass-sized.git"}, "scripts": {"postpublish": "git push origin --follow-tags", "postversion": "npm publish", "preversion": "npm test", "snap": "tap", "test": "tap"}, "tap": {"check-coverage": true}, "version": "1.0.3"}