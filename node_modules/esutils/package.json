{"_args": [["esutils@2.0.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "esutils@2.0.2", "_id": "esutils@2.0.2", "_inBundle": false, "_integrity": "sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs=", "_location": "/esutils", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "esutils@2.0.2", "name": "esutils", "escapedName": "esutils", "rawSpec": "2.0.2", "saveSpec": null, "fetchSpec": "2.0.2"}, "_requiredBy": ["/babel-code-frame", "/babel-plugin-transform-vue-jsx", "/babel-types"], "_resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "_spec": "2.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "bugs": {"url": "https://github.com/estools/esutils/issues"}, "description": "utility box for ECMAScript language tools", "devDependencies": {"chai": "~1.7.2", "coffee-script": "~1.6.3", "jshint": "2.6.3", "mocha": "~2.2.1", "regenerate": "~1.2.1", "unicode-7.0.0": "^0.1.5"}, "directories": {"lib": "./lib"}, "engines": {"node": ">=0.10.0"}, "files": ["LICENSE.BSD", "README.md", "lib"], "homepage": "https://github.com/estools/esutils", "licenses": [{"type": "BSD", "url": "http://github.com/estools/esutils/raw/master/LICENSE.BSD"}], "main": "lib/utils.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://github.com/Constellation"}], "name": "esutils", "repository": {"type": "git", "url": "git+ssh://**************/estools/esutils.git"}, "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "lint": "jshint lib/*.js", "test": "npm run-script lint && npm run-script unit-test", "unit-test": "mocha --compilers coffee:coffee-script -R spec"}, "version": "2.0.2"}