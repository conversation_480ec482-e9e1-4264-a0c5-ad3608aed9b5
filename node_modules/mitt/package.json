{"_from": "mitt@1.1.2", "_id": "mitt@1.1.2", "_inBundle": false, "_integrity": "sha512-3btxP0O9iGADGWAkteQ8mzDtEspZqu4I32y4GZYCV5BrwtzdcRpF4dQgNdJadCrbBx7Lu6Sq9AVrerMHR0Hkmw==", "_location": "/mitt", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "mitt@1.1.2", "name": "mitt", "escapedName": "mitt", "rawSpec": "1.1.2", "saveSpec": null, "fetchSpec": "1.1.2"}, "_requiredBy": ["/svg-baker-runtime"], "_resolved": "https://registry.npmjs.org/mitt/-/mitt-1.1.2.tgz", "_shasum": "380e61480d6a615b660f07abb60d51e0a4e4bed6", "_spec": "mitt@1.1.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/svg-baker-runtime", "authors": ["<PERSON> <<EMAIL>>"], "bugs": {"url": "https://github.com/developit/mitt/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Tiny 200b functional Event Emitter / pubsub.", "devDependencies": {"babel-core": "^6.9.1", "babel-eslint": "^7.1.1", "babel-plugin-transform-flow-strip-types": "^6.21.0", "babel-preset-es2015": "^6.9.0", "babel-preset-stage-0": "^6.5.0", "babel-register": "^6.9.0", "chai": "^3.5.0", "documentation": "^4.0.0-beta4", "eslint": "^3.13.1", "flow-bin": "^0.38.0", "gzip-size-cli": "^1.0.0", "mocha": "^3.2.0", "npm-run-all": "^2.1.1", "pretty-bytes-cli": "^2.0.0", "rimraf": "^2.5.2", "rollup": "^0.41.4", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-flow": "^1.1.1", "sinon": "^1.17.4", "sinon-chai": "^2.8.0", "standard-version": "^4.0.0", "strip-json-comments-cli": "^1.0.1", "uglify-js": "^2.6.2"}, "eslintConfig": {"parser": "babel-es<PERSON>", "extends": "eslint:recommended", "env": {"browser": true, "mocha": true, "es6": true}, "globals": {"expect": true}, "rules": {"semi": [2, "always"]}}, "files": ["src", "dist", "mitt.d.ts"], "homepage": "https://github.com/developit/mitt", "jsnext:main": "dist/mitt.es.js", "keywords": ["events", "eventemitter", "pubsub"], "license": "MIT", "main": "dist/mitt.js", "module": "dist/mitt.es.js", "name": "mitt", "repository": {"type": "git", "url": "git+https://github.com/developit/mitt.git"}, "scripts": {"build": "npm-run-all --silent clean -p rollup -p minify:* -s docs size", "bump": "standard-version", "clean": "<PERSON><PERSON><PERSON> dist", "docs": "documentation readme src/index.js --section API -q", "lint": "eslint src test", "minify:cjs": "uglifyjs $npm_package_main -cm toplevel -o $npm_package_main -p relative --in-source-map ${npm_package_main}.map --source-map ${npm_package_main}.map", "minify:umd": "uglifyjs $npm_package_umd_main -cm -o $npm_package_umd_main -p relative --in-source-map ${npm_package_umd_main}.map --source-map ${npm_package_umd_main}.map", "release": "npm run build -s && npm run bump && git push --follow-tags origin master && npm publish", "rollup": "rollup -c", "size": "echo \"Gzipped Size: $(strip-json-comments --no-whitespace $npm_package_main | gzip-size | pretty-bytes)\"", "test": "flow && npm run lint && npm run testonly", "testonly": "mocha --compilers js:babel-register test/**/*.js"}, "typings": "./mitt.d.ts", "umd:main": "dist/mitt.umd.js", "version": "1.1.2"}