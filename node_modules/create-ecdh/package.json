{"_args": [["create-ecdh@4.0.4", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "create-ecdh@4.0.4", "_id": "create-ecdh@4.0.4", "_inBundle": false, "_integrity": "sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A==", "_location": "/create-ecdh", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "create-ecdh@4.0.4", "name": "create-ecdh", "escapedName": "create-ecdh", "rawSpec": "4.0.4", "saveSpec": null, "fetchSpec": "4.0.4"}, "_requiredBy": ["/crypto-browserify"], "_resolved": "https://registry.npmjs.org/create-ecdh/-/create-ecdh-4.0.4.tgz", "_spec": "4.0.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>"}, "browser": "browser.js", "bugs": {"url": "https://github.com/crypto-browserify/createECDH/issues"}, "dependencies": {"bn.js": "^4.1.0", "elliptic": "^6.5.3"}, "description": "createECDH but browserifiable", "devDependencies": {"standard": "^5.4.1", "tap-spec": "^1.0.1", "tape": "^3.0.1"}, "homepage": "https://github.com/crypto-browserify/createECDH", "keywords": ["diffie", "hellman", "di<PERSON><PERSON><PERSON><PERSON>", "ECDH"], "license": "MIT", "main": "index.js", "name": "create-ecdh", "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/createECDH.git"}, "scripts": {"test": "standard && node test.js | tspec"}, "version": "4.0.4"}