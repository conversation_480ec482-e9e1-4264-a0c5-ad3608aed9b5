{"_from": "is-weakmap@^2.0.2", "_id": "is-weakmap@2.0.2", "_inBundle": false, "_integrity": "sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==", "_location": "/is-weakmap", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-weakmap@^2.0.2", "name": "is-weakmap", "escapedName": "is-weakmap", "rawSpec": "^2.0.2", "saveSpec": null, "fetchSpec": "^2.0.2"}, "_requiredBy": ["/which-collection"], "_resolved": "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz", "_shasum": "bf72615d649dfe5f699079c54b83e47d1ae19cfd", "_spec": "is-weakmap@^2.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/which-collection", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-weakmap/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Is this value a JS WeakMap? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.15.0", "@ljharb/eslint-config": "^21.1.0", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-weakmap#readme", "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "license": "MIT", "main": "index.js", "name": "is-weakmap", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakmap.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "tests-only": "nyc tape 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "tests:shims": "nyc tape --require=es5-shim --require=es5-shim 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": true, "version": "2.0.2"}