{"_from": "path-type@^3.0.0", "_id": "path-type@3.0.0", "_inBundle": false, "_integrity": "sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==", "_location": "/path-type", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "path-type@^3.0.0", "name": "path-type", "escapedName": "path-type", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/dir-glob"], "_resolved": "https://registry.npmjs.org/path-type/-/path-type-3.0.0.tgz", "_shasum": "cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f", "_spec": "path-type@^3.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/dir-glob", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/path-type/issues"}, "bundleDependencies": false, "dependencies": {"pify": "^3.0.0"}, "deprecated": false, "description": "Check if a path is a file, directory, or symlink", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/path-type#readme", "keywords": ["path", "fs", "type", "is", "check", "directory", "dir", "file", "filepath", "symlink", "symbolic", "link", "stat", "stats", "filesystem"], "license": "MIT", "name": "path-type", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-type.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.0"}