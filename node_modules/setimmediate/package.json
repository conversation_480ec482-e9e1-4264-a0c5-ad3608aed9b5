{"_args": [["setimmediate@1.0.5", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "setimmediate@1.0.5", "_id": "setimmediate@1.0.5", "_inBundle": false, "_integrity": "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==", "_location": "/setimmediate", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "setimmediate@1.0.5", "name": "setimmediate", "escapedName": "setimmediate", "rawSpec": "1.0.5", "saveSpec": null, "fetchSpec": "1.0.5"}, "_requiredBy": ["/timers-browserify"], "_resolved": "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz", "_spec": "1.0.5", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "YuzuJS"}, "bugs": {"url": "https://github.com/YuzuJS/setImmediate/issues"}, "contributors": [{"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me"}, {"name": "Donavon West", "email": "<EMAIL>", "url": "http://donavon.com"}, {"name": "Yaffle"}], "description": "A shim for the setImmediate efficient script yielding API", "devDependencies": {"http-server": "~0.6.1", "jshint": "^2.5.0", "mocha": "~1.18.2", "opener": "^1.3", "zuul": "^1.6.4"}, "files": ["setImmediate.js"], "homepage": "https://github.com/YuzuJS/setImmediate#readme", "license": "MIT", "main": "setImmediate.js", "name": "setimmediate", "repository": {"type": "git", "url": "git+https://github.com/YuzuJS/setImmediate.git"}, "scripts": {"lint": "jshint setImmediate.js", "test": "mocha test/tests.js", "test-browser": "opener http://localhost:9008/__zuul && zuul test/tests.js --ui mocha-bdd --local 9008", "test-browser-only": "opener http://localhost:9007/test/browserOnly/index.html && http-server . -p 9007"}, "version": "1.0.5"}