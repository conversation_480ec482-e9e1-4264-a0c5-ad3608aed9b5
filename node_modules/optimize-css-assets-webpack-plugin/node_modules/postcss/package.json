{"_from": "postcss@^7.0.0", "_id": "postcss@7.0.39", "_inBundle": false, "_integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "_location": "/optimize-css-assets-webpack-plugin/postcss", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "postcss@^7.0.0", "name": "postcss", "escapedName": "postcss", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}, "_requiredBy": ["/optimize-css-assets-webpack-plugin/cssnano"], "_resolved": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "_shasum": "9624375d965630e2e1f2c02a935c82a59cb48309", "_spec": "postcss@^7.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/optimize-css-assets-webpack-plugin/node_modules/cssnano", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": {"./lib/terminal-highlight": false, "fs": false}, "bugs": {"url": "https://github.com/postcss/postcss/issues"}, "bundleDependencies": false, "dependencies": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}, "deprecated": false, "description": "Tool for transforming styles with JS plugins", "engines": {"node": ">=6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "homepage": "https://postcss.org/", "keywords": ["css", "postcss", "rework", "preprocessor", "parser", "source map", "transform", "manipulation", "transpiler"], "license": "MIT", "main": "lib/postcss", "name": "postcss", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss.git"}, "types": "lib/postcss.d.ts", "version": "7.0.39"}