{"_args": [["ms@2.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "ms@2.0.0", "_id": "ms@2.0.0", "_inBundle": false, "_integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "_location": "/ms", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ms@2.0.0", "name": "ms", "escapedName": "ms", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/debug"], "_resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "_spec": "2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "description": "Tiny milisecond conversion utility", "devDependencies": {"eslint": "3.19.0", "expect.js": "0.3.1", "husky": "0.13.3", "lint-staged": "3.4.1", "mocha": "3.4.1"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "files": ["index.js"], "homepage": "https://github.com/zeit/ms#readme", "license": "MIT", "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "main": "./index", "name": "ms", "repository": {"type": "git", "url": "git+https://github.com/zeit/ms.git"}, "scripts": {"lint": "eslint lib/* bin/*", "precommit": "lint-staged", "test": "mocha tests.js"}, "version": "2.0.0"}