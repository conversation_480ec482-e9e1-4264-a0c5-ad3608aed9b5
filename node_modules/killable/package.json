{"_args": [["killable@1.0.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "killable@1.0.1", "_id": "killable@1.0.1", "_inBundle": false, "_integrity": "sha512-LzqtLKlUwirEUyl/nicirVmNiPvYs7l5n8wOPP7fyJVpUPkvCnW/vuiXGpylGUlnPDnB7311rARzAt3Mhswpjg==", "_location": "/killable", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "killable@1.0.1", "name": "killable", "escapedName": "killable", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://registry.npmjs.org/killable/-/killable-1.0.1.tgz", "_spec": "1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/marten-de-vries/killable/issues"}, "description": "Keeps track of a server's open sockets so they can be destroyed at a moment's notice.", "homepage": "https://github.com/marten-de-vries/killable#readme", "keywords": ["express", "http", "server", "socket", "kill", "truncate", "destroy", "restart", "shutdown", "immeadiately"], "license": "ISC", "main": "index.js", "name": "killable", "repository": {"type": "git", "url": "git+https://github.com/marten-de-vries/killable.git"}, "version": "1.0.1"}