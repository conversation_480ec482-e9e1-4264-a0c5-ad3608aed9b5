{"_args": [["json-schema@0.4.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "json-schema@0.4.0", "_id": "json-schema@0.4.0", "_inBundle": false, "_integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==", "_location": "/json-schema", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "json-schema@0.4.0", "name": "json-schema", "escapedName": "json-schema", "rawSpec": "0.4.0", "saveSpec": null, "fetchSpec": "0.4.0"}, "_requiredBy": ["/jsprim"], "_resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz", "_spec": "0.4.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/kriszyp/json-schema/issues"}, "description": "JSON Schema validation and specifications", "devDependencies": {"vows": "*"}, "directories": {"lib": "./lib"}, "files": ["lib"], "homepage": "https://github.com/kriszyp/json-schema#readme", "keywords": ["json", "schema"], "license": "(AFL-2.1 OR BSD-3-Clause)", "main": "./lib/validate.js", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}], "name": "json-schema", "repository": {"type": "git", "url": "git+ssh://**************/kriszyp/json-schema.git"}, "scripts": {"test": "vows --spec test/*.js"}, "version": "0.4.0"}