{"_from": "has-value@^1.0.0", "_id": "has-value@1.0.0", "_inBundle": false, "_integrity": "sha512-IBXk4GTsLYdQ7Rvt+GRBrFSVEkmuOUy4re0Xjd9kJSUQpnTrWR4/y9RpfexN9vkAPMFuQoeWKwqzPozRTlasGw==", "_location": "/has-value", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "has-value@^1.0.0", "name": "has-value", "escapedName": "has-value", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/cache-base"], "_resolved": "https://registry.npmjs.org/has-value/-/has-value-1.0.0.tgz", "_shasum": "18b281da585b1c5c51def24c930ed29a0be6b177", "_spec": "has-value@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/cache-base", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/has-value/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://linkedin.com/in/harrisonrm"}], "dependencies": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}, "deprecated": false, "description": "Returns true if a value exists, false if empty. Works with deeply nested values using object paths.", "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.4.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/has-value", "keywords": ["array", "boolean", "empty", "find", "function", "has", "hasOwn", "javascript", "js", "key", "keys", "node.js", "null", "number", "object", "properties", "property", "string", "type", "util", "utilities", "utility", "value"], "license": "MIT", "main": "index.js", "name": "has-value", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/has-value.git"}, "scripts": {"test": "mocha"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["define-property", "get-value", "set-value", "unset-value"]}, "reflinks": [], "lint": {"reflinks": true}}, "version": "1.0.0"}