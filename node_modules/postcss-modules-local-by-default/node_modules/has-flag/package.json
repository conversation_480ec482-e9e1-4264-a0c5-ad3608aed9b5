{"_args": [["has-flag@3.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "has-flag@3.0.0", "_id": "has-flag@3.0.0", "_inBundle": false, "_integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==", "_location": "/postcss-modules-local-by-default/has-flag", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "has-flag@3.0.0", "name": "has-flag", "escapedName": "has-flag", "rawSpec": "3.0.0", "saveSpec": null, "fetchSpec": "3.0.0"}, "_requiredBy": ["/postcss-modules-local-by-default/supports-color"], "_resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "_spec": "3.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/has-flag/issues"}, "description": "Check if argv has a specific flag", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/has-flag#readme", "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "license": "MIT", "name": "has-flag", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/has-flag.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.0"}