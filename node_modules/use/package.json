{"_args": [["use@3.1.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "use@3.1.1", "_id": "use@3.1.1", "_inBundle": false, "_integrity": "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ==", "_location": "/use", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "use@3.1.1", "name": "use", "escapedName": "use", "rawSpec": "3.1.1", "saveSpec": null, "fetchSpec": "3.1.1"}, "_requiredBy": ["/snapdragon"], "_resolved": "https://registry.npmjs.org/use/-/use-3.1.1.tgz", "_spec": "3.1.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/use/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://i.am.charlike.online"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "description": "Easily add plugin support to your node.js application.", "devDependencies": {"base-plugins": "^1.0.0", "define-property": "^2.0.0", "extend-shallow": "^3.0.1", "gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "mocha": "^4.0.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/use", "keywords": ["use"], "license": "MIT", "main": "index.js", "name": "use", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/use.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["base", "base-plugins", "ware"]}, "reflinks": ["verb", "ware"], "lint": {"reflinks": true}}, "version": "3.1.1"}