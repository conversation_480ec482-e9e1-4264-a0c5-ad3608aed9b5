{"_from": "object.pick@^1.3.0", "_id": "object.pick@1.3.0", "_inBundle": false, "_integrity": "sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==", "_location": "/object.pick", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "object.pick@^1.3.0", "name": "object.pick", "escapedName": "object.pick", "rawSpec": "^1.3.0", "saveSpec": null, "fetchSpec": "^1.3.0"}, "_requiredBy": ["/http-proxy-middleware/micromatch", "/micromatch", "/nanomatch", "/watchpack-chokidar2/micromatch", "/webpack-dev-server/micromatch"], "_resolved": "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz", "_shasum": "87a10ac4c1694bd2e1cbf53591a66141fb5dd747", "_spec": "object.pick@^1.3.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/object.pick/issues"}, "bundleDependencies": false, "dependencies": {"isobject": "^3.0.1"}, "deprecated": false, "description": "Returns a filtered copy of an object with only the specified keys, similar to `_.pick` from lodash / underscore.", "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.1.2", "vinyl": "^2.0.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/object.pick", "keywords": ["object", "pick"], "license": "MIT", "main": "index.js", "name": "object.pick", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/object.pick.git"}, "scripts": {"test": "mocha"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["extend-shallow", "get-value", "mixin-deep", "set-value"], "highlight": "object.omit"}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "version": "1.3.0"}