{"_from": "friendly-errors-webpack-plugin@^1.6.1", "_id": "friendly-errors-webpack-plugin@1.7.0", "_inBundle": false, "_integrity": "sha512-K27M3VK30wVoOarP651zDmb93R9zF28usW4ocaK3mfQeIEI5BPht/EzZs5E8QLLwbLRJQMwscAjDxYPb1FuNiw==", "_location": "/friendly-errors-webpack-plugin", "_phantomChildren": {"escape-string-regexp": "1.0.5", "has-ansi": "2.0.0", "strip-ansi": "3.0.1"}, "_requested": {"type": "range", "registry": true, "raw": "friendly-errors-webpack-plugin@^1.6.1", "name": "friendly-errors-webpack-plugin", "escapedName": "friendly-errors-webpack-plugin", "rawSpec": "^1.6.1", "saveSpec": null, "fetchSpec": "^1.6.1"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.7.0.tgz", "_shasum": "efc86cbb816224565861a1be7a9d84d0aafea136", "_spec": "friendly-errors-webpack-plugin@^1.6.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/geowarin/friendly-errors-webpack-plugin/issues"}, "bundleDependencies": false, "dependencies": {"chalk": "^1.1.3", "error-stack-parser": "^2.0.0", "string-width": "^2.0.0"}, "deprecated": false, "description": "Recognizes certain classes of webpack errors and cleans, aggregates and prioritizes them to provide a better Developer Experience", "devDependencies": {"babel-core": "^6.23.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.3.0", "babel-plugin-transform-async-to-generator": "^6.22.0", "babel-preset-react": "^6.23.0", "eslint": "^3.16.1", "eslint-loader": "^1.6.1", "expect": "^1.20.2", "jest": "^18.1.0", "memory-fs": "^0.4.1", "webpack": "^2.2.1"}, "files": ["src", "index.js"], "homepage": "https://github.com/geowarin/friendly-errors-webpack-plugin#readme", "keywords": ["friendly", "errors", "webpack", "plugin"], "license": "MIT", "main": "index.js", "name": "friendly-errors-webpack-plugin", "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/geowarin/friendly-errors-webpack-plugin.git"}, "scripts": {"test": "eslint --ignore-pattern test/* && jest"}, "version": "1.7.0"}