{"_from": "strip-ansi@^4.0.0", "_id": "strip-ansi@4.0.0", "_inBundle": false, "_integrity": "sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow==", "_location": "/friendly-errors-webpack-plugin/string-width/strip-ansi", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "strip-ansi@^4.0.0", "name": "strip-ansi", "escapedName": "strip-ansi", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/friendly-errors-webpack-plugin/string-width"], "_resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "_shasum": "a8479022eb1ac368a871389b635262c505ee368f", "_spec": "strip-ansi@^4.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/friendly-errors-webpack-plugin/node_modules/string-width", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "bundleDependencies": false, "dependencies": {"ansi-regex": "^3.0.0"}, "deprecated": false, "description": "Strip ANSI escape codes", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/chalk/strip-ansi#readme", "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "strip-ansi", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "scripts": {"test": "xo && ava"}, "version": "4.0.0"}