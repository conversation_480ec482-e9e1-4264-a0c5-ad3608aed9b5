{"_from": "string-width@^2.0.0", "_id": "string-width@2.1.1", "_inBundle": false, "_integrity": "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==", "_location": "/friendly-errors-webpack-plugin/string-width", "_phantomChildren": {"ansi-regex": "3.0.1"}, "_requested": {"type": "range", "registry": true, "raw": "string-width@^2.0.0", "name": "string-width", "escapedName": "string-width", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/friendly-errors-webpack-plugin"], "_resolved": "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz", "_shasum": "ab93f27a8dc13d28cac815c462143a6d9012ae9e", "_spec": "string-width@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/friendly-errors-webpack-plugin", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "bundleDependencies": false, "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "deprecated": false, "description": "Get the visual width of a string - the number of columns required to display it", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/string-width#readme", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "license": "MIT", "name": "string-width", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-width.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.1"}