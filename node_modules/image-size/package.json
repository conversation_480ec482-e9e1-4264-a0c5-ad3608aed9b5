{"_from": "image-size@^0.5.1", "_id": "image-size@0.5.5", "_inBundle": false, "_integrity": "sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==", "_location": "/image-size", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "image-size@^0.5.1", "name": "image-size", "escapedName": "image-size", "rawSpec": "^0.5.1", "saveSpec": null, "fetchSpec": "^0.5.1"}, "_requiredBy": ["/svg-baker"], "_resolved": "https://registry.npmjs.org/image-size/-/image-size-0.5.5.tgz", "_shasum": "09dfd4ab9d20e29eb1c3e80b8990378df9e3cb9c", "_spec": "image-size@^0.5.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/svg-baker", "author": {"name": "netroy", "email": "<EMAIL>", "url": "http://netroy.in/"}, "bin": {"image-size": "bin/image-size.js"}, "bugs": {"url": "https://github.com/image-size/image-size/issues"}, "bundleDependencies": false, "deprecated": false, "description": "get dimensions of any image file", "devDependencies": {"escomplex-js": "^1.2.0", "expect.js": "^0.3.1", "glob": "^7.1.1", "istanbul": "^1.1.0-alpha.1", "jshint": "^2.9.4", "mocha": "^3.4.1", "sinon": "^2.2.0"}, "engines": {"node": ">=0.10.0"}, "files": ["bin", "lib"], "homepage": "https://github.com/image-size/image-size#readme", "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "png", "jpeg", "bmp", "gif", "psd", "tiff", "webp", "svg"], "license": "MIT", "main": "lib/index.js", "name": "image-size", "repository": {"type": "git", "url": "git+https://github.com/image-size/image-size.git"}, "scripts": {"coverage": "istanbul cover _mocha specs", "pretest": "j<PERSON>t", "test": "mocha specs"}, "version": "0.5.5"}