{"_from": "js-cookie@^2.2.0", "_id": "js-cookie@2.2.1", "_inBundle": false, "_integrity": "sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ==", "_location": "/js-cookie", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "js-cookie@^2.2.0", "name": "js-cookie", "escapedName": "js-cookie", "rawSpec": "^2.2.0", "saveSpec": null, "fetchSpec": "^2.2.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/js-cookie/-/js-cookie-2.2.1.tgz", "_shasum": "69e106dc5d5806894562902aa5baec3744e9b2b8", "_spec": "js-cookie@^2.2.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/js-cookie/js-cookie/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A simple, lightweight JavaScript API for handling cookies", "devDependencies": {"grunt": "1.0.3", "grunt-compare-size": "0.4.2", "grunt-contrib-connect": "2.0.0", "grunt-contrib-nodeunit": "2.0.0", "grunt-contrib-qunit": "2.0.0", "grunt-contrib-uglify": "2.3.0", "grunt-contrib-watch": "1.1.0", "grunt-eslint": "21.0.0", "grunt-saucelabs": "9.0.0", "gzip-js": "0.3.2", "qunitjs": "1.23.1", "requirejs": "2.3.5"}, "directories": {"test": "test"}, "files": ["src/**/*.js", "SERVER_SIDE.md", "CONTRIBUTING.md"], "homepage": "https://github.com/js-cookie/js-cookie#readme", "keywords": ["cookie", "cookies", "browser", "amd", "commonjs", "client", "js-cookie", "browserify"], "license": "MIT", "main": "src/js.cookie.js", "name": "js-cookie", "repository": {"type": "git", "url": "git://github.com/js-cookie/js-cookie.git"}, "scripts": {"test": "grunt test"}, "version": "2.2.1"}