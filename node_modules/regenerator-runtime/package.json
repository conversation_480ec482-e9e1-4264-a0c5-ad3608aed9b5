{"_args": [["regenerator-runtime@0.11.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "regenerator-runtime@0.11.1", "_id": "regenerator-runtime@0.11.1", "_inBundle": false, "_integrity": "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==", "_location": "/regenerator-runtime", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "regenerator-runtime@0.11.1", "name": "regenerator-runtime", "escapedName": "regenerator-runtime", "rawSpec": "0.11.1", "saveSpec": null, "fetchSpec": "0.11.1"}, "_requiredBy": ["/babel-runtime"], "_resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz", "_spec": "0.11.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "keywords": ["regenerator", "runtime", "generator", "async"], "license": "MIT", "main": "runtime-module.js", "name": "regenerator-runtime", "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "version": "0.11.1"}