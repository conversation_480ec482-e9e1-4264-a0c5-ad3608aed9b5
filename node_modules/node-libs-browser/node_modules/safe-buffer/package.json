{"_from": "safe-buffer@~5.1.1", "_id": "safe-buffer@5.1.2", "_inBundle": false, "_integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "_location": "/node-libs-browser/safe-buffer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "safe-buffer@~5.1.1", "name": "safe-buffer", "escapedName": "safe-buffer", "rawSpec": "~5.1.1", "saveSpec": null, "fetchSpec": "~5.1.1"}, "_requiredBy": ["/node-libs-browser/readable-stream", "/node-libs-browser/readable-stream/string_decoder"], "_resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "_shasum": "991ec69d296e0313747d59bdfd2b745c35f8828d", "_spec": "safe-buffer@~5.1.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/node-libs-browser/node_modules/readable-stream", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Safer Node.js Buffer API", "devDependencies": {"standard": "*", "tape": "^4.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "name": "safe-buffer", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test/*.js"}, "types": "index.d.ts", "version": "5.1.2"}