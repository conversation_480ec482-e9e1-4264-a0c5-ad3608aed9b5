{"_args": [["node-libs-browser@2.2.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "node-libs-browser@2.2.1", "_id": "node-libs-browser@2.2.1", "_inBundle": false, "_integrity": "sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q==", "_location": "/node-libs-browser", "_phantomChildren": {"core-util-is": "1.0.2", "inherits": "2.0.4", "isarray": "1.0.0", "process-nextick-args": "2.0.1", "util-deprecate": "1.0.2"}, "_requested": {"type": "version", "registry": true, "raw": "node-libs-browser@2.2.1", "name": "node-libs-browser", "escapedName": "node-libs-browser", "rawSpec": "2.2.1", "saveSpec": null, "fetchSpec": "2.2.1"}, "_requiredBy": ["/webpack"], "_resolved": "https://registry.npmjs.org/node-libs-browser/-/node-libs-browser-2.2.1.tgz", "_spec": "2.2.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack/node-libs-browser/issues"}, "dependencies": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^3.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.1", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.11.0", "vm-browserify": "^1.0.1"}, "description": "The node core libs for in browser usage.", "files": ["index.js", "mock/"], "homepage": "http://github.com/webpack/node-libs-browser", "license": "MIT", "main": "index.js", "name": "node-libs-browser", "repository": {"type": "git", "url": "git+https://github.com/webpack/node-libs-browser.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "2.2.1"}