{"_args": [["postcss-discard-duplicates@2.1.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "postcss-discard-duplicates@2.1.0", "_id": "postcss-discard-duplicates@2.1.0", "_inBundle": false, "_integrity": "sha512-+lk5W1uqO8qIUTET+UETgj9GWykLC3LOldr7EehmymV0Wu36kyoHimC4cILrAAYpHQ+fr4ypKcWcVNaGzm0reA==", "_location": "/postcss-discard-duplicates", "_phantomChildren": {"escape-string-regexp": "1.0.5", "has-ansi": "2.0.0", "js-base64": "2.6.4", "strip-ansi": "3.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "postcss-discard-duplicates@2.1.0", "name": "postcss-discard-duplicates", "escapedName": "postcss-discard-duplicates", "rawSpec": "2.1.0", "saveSpec": null, "fetchSpec": "2.1.0"}, "_requiredBy": ["/cssnano"], "_resolved": "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-2.1.0.tgz", "_spec": "2.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "ava": {"require": "babel-register"}, "bugs": {"url": "https://github.com/ben-eb/postcss-discard-duplicates/issues"}, "dependencies": {"postcss": "^5.0.4"}, "description": "Discard duplicate rules in your CSS files with PostCSS.", "devDependencies": {"all-contributors-cli": "^3.0.5", "ava": "^0.17.0", "babel-cli": "^6.3.17", "babel-core": "^6.3.26", "babel-plugin-add-module-exports": "^0.2.0", "babel-preset-es2015": "^6.5.0", "babel-preset-es2015-loose": "^7.0.0", "babel-preset-stage-0": "^6.3.13", "babel-register": "^6.9.0", "del-cli": "^0.2.0", "eslint": "^3.0.0", "eslint-config-cssnano": "^3.0.0", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-import": "^2.0.1"}, "eslintConfig": {"extends": "cssnano"}, "files": ["dist", "LICENSE-MIT"], "homepage": "https://github.com/ben-eb/postcss-discard-duplicates", "keywords": ["css", "dedupe", "optimise", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-discard-duplicates", "repository": {"type": "git", "url": "git+https://github.com/ben-eb/postcss-discard-duplicates.git"}, "scripts": {"contributorAdd": "all-contributors add", "contributorGenerate": "all-contributors generate", "prepublish": "del-cli dist && babel src --out-dir dist --ignore /__tests__/", "pretest": "eslint src", "test": "ava src/__tests__", "test-012": "ava src/__tests__"}, "version": "2.1.0"}