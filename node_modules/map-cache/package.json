{"_from": "map-cache@^0.2.2", "_id": "map-cache@0.2.2", "_inBundle": false, "_integrity": "sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg==", "_location": "/map-cache", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "map-cache@^0.2.2", "name": "map-cache", "escapedName": "map-cache", "rawSpec": "^0.2.2", "saveSpec": null, "fetchSpec": "^0.2.2"}, "_requiredBy": ["/fragment-cache", "/snapdragon"], "_resolved": "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz", "_shasum": "c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf", "_spec": "map-cache@^0.2.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/snapdragon", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/map-cache/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Basic cache object for storing key-value pairs.", "devDependencies": {"gulp-format-md": "^0.1.9", "should": "^8.3.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/map-cache", "keywords": ["cache", "get", "has", "object", "set", "storage", "store"], "license": "MIT", "main": "index.js", "name": "map-cache", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/map-cache.git"}, "scripts": {"test": "mocha"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["config-cache", "option-cache", "cache-base"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "version": "0.2.2"}