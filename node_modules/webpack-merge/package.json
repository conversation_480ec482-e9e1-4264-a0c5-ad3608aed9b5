{"_args": [["webpack-merge@4.2.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "webpack-merge@4.2.2", "_id": "webpack-merge@4.2.2", "_inBundle": false, "_integrity": "sha512-TUE1UGoTX2Cd42j3krGYqObZbOD+xF7u28WB7tfUordytSjbWTIjK/8V0amkBfTYN4/pB/GIDlJZZ657BGG19g==", "_location": "/webpack-merge", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "webpack-merge@4.2.2", "name": "webpack-merge", "escapedName": "webpack-merge", "rawSpec": "4.2.2", "saveSpec": null, "fetchSpec": "4.2.2"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.2.2.tgz", "_spec": "4.2.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/survivejs/webpack-merge/issues"}, "dependencies": {"lodash": "^4.17.15"}, "description": "Variant of merge that's useful for webpack configuration", "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-lodash": "^3.3.2", "babel-preset-es2015": "^6.24.1", "copy-webpack-plugin": "^4.4.1", "eslint": "^3.19.0", "eslint-config-airbnb": "^14.1.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-jsx-a11y": "^3.0.2", "eslint-plugin-react": "^6.10.3", "git-prepush-hook": "^1.0.2", "istanbul": "^0.4.5", "mocha": "^3.5.3", "npm-watch": "^0.1.9", "webpack": "^1.15.0"}, "files": ["lib"], "homepage": "https://github.com/survivejs/webpack-merge", "keywords": ["webpack", "merge"], "license": "MIT", "main": "lib/index.js", "name": "webpack-merge", "pre-push": ["test:lint", "build", "test"], "repository": {"type": "git", "url": "git+https://github.com/survivejs/webpack-merge.git"}, "scripts": {"build": "babel src -d lib", "preversion": "npm run test:lint && npm run build && npm test && git commit --allow-empty -am \"Update lib\"", "test": "mocha tests/test-*", "test:coverage": "istanbul cover node_modules/.bin/_mocha tests/test-*", "test:lint": "eslint src/ tests/ --cache", "watch": "npm-watch"}, "version": "4.2.2", "watch": {"build": {"patterns": ["src/**/*.js"]}, "test": {"patterns": ["src/**/*.js", "tests/**/*.js"]}, "test:lint": {"patterns": ["src/**/*.js", "tests/**/*.js"]}}}