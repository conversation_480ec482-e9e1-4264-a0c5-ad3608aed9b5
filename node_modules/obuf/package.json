{"_from": "obuf@^1.1.2", "_id": "obuf@1.1.2", "_inBundle": false, "_integrity": "sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==", "_location": "/obuf", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "obuf@^1.1.2", "name": "obuf", "escapedName": "obuf", "rawSpec": "^1.1.2", "saveSpec": null, "fetchSpec": "^1.1.2"}, "_requiredBy": ["/hpack.js", "/spdy-transport"], "_resolved": "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz", "_shasum": "09bea3343d41859ebd446292d11c9d4db619084e", "_spec": "obuf@^1.1.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/spdy-transport", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/indutny/offset-buffer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Byte buffer specialized for data in chunks with special cases for dropping bytes in the front, merging bytes in to various integer types and abandoning buffer without penalty for previous chunk merges.", "devDependencies": {"mocha": "^1.21.4"}, "homepage": "https://github.com/indutny/offset-buffer", "keywords": ["Offset", "<PERSON><PERSON><PERSON>", "reader"], "license": "MIT", "main": "index.js", "name": "obuf", "repository": {"type": "git", "url": "git+ssh://**************/indutny/offset-buffer.git"}, "scripts": {"test": "mocha test/**/*-test.js"}, "version": "1.1.2"}