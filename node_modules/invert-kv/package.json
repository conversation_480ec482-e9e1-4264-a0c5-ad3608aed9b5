{"_from": "invert-kv@^1.0.0", "_id": "invert-kv@1.0.0", "_inBundle": false, "_integrity": "sha512-xgs2NH9AE66ucSq4cNG1nhSFghr5l6tdL15Pk+jl46bmmBapgoaY/AacXyaDznAqmGL99TiLSQgO/XazFSKYeQ==", "_location": "/invert-kv", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "invert-kv@^1.0.0", "name": "invert-kv", "escapedName": "invert-kv", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/lcid"], "_resolved": "https://registry.npmjs.org/invert-kv/-/invert-kv-1.0.0.tgz", "_shasum": "104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6", "_spec": "invert-kv@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/lcid", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/invert-kv/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Invert the key/value of an object. Example: {foo: 'bar'} → {bar: 'foo'}", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/invert-kv#readme", "keywords": ["object", "obj", "key", "value", "val", "kv", "invert"], "license": "MIT", "name": "invert-kv", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/invert-kv.git"}, "scripts": {"test": "mocha"}, "version": "1.0.0"}