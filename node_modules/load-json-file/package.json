{"_args": [["load-json-file@2.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "load-json-file@2.0.0", "_id": "load-json-file@2.0.0", "_inBundle": false, "_integrity": "sha512-3p6ZOGNbiX4CdvEd1VcE6yi78UrGNpjHO33noGwHCnT/o2fyllJDepsm8+mFFv/DvtwFHht5HIHSyOy5a+ChVQ==", "_location": "/load-json-file", "_phantomChildren": {"error-ex": "1.3.2"}, "_requested": {"type": "version", "registry": true, "raw": "load-json-file@2.0.0", "name": "load-json-file", "escapedName": "load-json-file", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/webpack/read-pkg"], "_resolved": "https://registry.npmjs.org/load-json-file/-/load-json-file-2.0.0.tgz", "_spec": "2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/load-json-file/issues"}, "dependencies": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "strip-bom": "^3.0.0"}, "description": "Read and parse a JSON file", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/load-json-file#readme", "keywords": ["read", "json", "parse", "file", "fs", "graceful", "load"], "license": "MIT", "name": "load-json-file", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/load-json-file.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.0", "xo": {"esnext": true}}