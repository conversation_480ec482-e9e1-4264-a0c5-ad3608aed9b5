{"_args": [["parse-json@2.2.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "parse-json@2.2.0", "_id": "parse-json@2.2.0", "_inBundle": false, "_integrity": "sha512-QR/GGaKCkhwk1ePQNYDRKYZ3mwU9ypsKhB0XyFnLQdomyEqk3e8wpW3V5Jp88zbxK4n5ST1nqo+g9juTpownhQ==", "_location": "/load-json-file/parse-json", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "parse-json@2.2.0", "name": "parse-json", "escapedName": "parse-json", "rawSpec": "2.2.0", "saveSpec": null, "fetchSpec": "2.2.0"}, "_requiredBy": ["/load-json-file"], "_resolved": "https://registry.npmjs.org/parse-json/-/parse-json-2.2.0.tgz", "_spec": "2.2.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "dependencies": {"error-ex": "^1.2.0"}, "description": "Parse JSO<PERSON> with more helpful errors", "devDependencies": {"ava": "0.0.4", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "vendor"], "homepage": "https://github.com/sindresorhus/parse-json#readme", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string", "str"], "license": "MIT", "name": "parse-json", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/parse-json.git"}, "scripts": {"test": "xo && node test.js"}, "version": "2.2.0", "xo": {"ignores": ["vendor/**"]}}