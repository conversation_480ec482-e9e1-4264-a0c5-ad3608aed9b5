{"_args": [["source-map@0.5.7", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "source-map@0.5.7", "_id": "source-map@0.5.7", "_inBundle": false, "_integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "_location": "/babel-core/source-map", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "source-map@0.5.7", "name": "source-map", "escapedName": "source-map", "rawSpec": "0.5.7", "saveSpec": null, "fetchSpec": "0.5.7"}, "_requiredBy": ["/babel-core"], "_resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "_spec": "0.5.7", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Generates and consumes source maps", "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "engines": {"node": ">=0.10.0"}, "files": ["source-map.js", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "homepage": "https://github.com/mozilla/source-map", "license": "BSD-3-<PERSON><PERSON>", "main": "./source-map.js", "name": "source-map", "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "scripts": {"build": "webpack --color", "test": "npm run build && node test/run-tests.js", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "typings": "source-map", "version": "0.5.7"}