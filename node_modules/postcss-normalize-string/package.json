{"_args": [["postcss-normalize-string@4.0.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "postcss-normalize-string@4.0.2", "_id": "postcss-normalize-string@4.0.2", "_inBundle": false, "_integrity": "sha512-RrERod97Dnwqq49WNz8qo66ps0swYZDSb6rM57kN2J+aoyEAJfZ6bMx0sx/F9TIEX0xthPGCmeyiam/jXif0eA==", "_location": "/postcss-normalize-string", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "postcss-normalize-string@4.0.2", "name": "postcss-normalize-string", "escapedName": "postcss-normalize-string", "rawSpec": "4.0.2", "saveSpec": null, "fetchSpec": "4.0.2"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "https://registry.npmjs.org/postcss-normalize-string/-/postcss-normalize-string-4.0.2.tgz", "_spec": "4.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "dependencies": {"has": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "description": "Normalize wrapping quotes for CSS string literals.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["dist", "LICENSE-MIT"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-normalize-string", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.2"}