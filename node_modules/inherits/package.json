{"_from": "inherits@2", "_id": "inherits@2.0.4", "_inBundle": false, "_integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "_location": "/inherits", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "inherits@2", "name": "inherits", "escapedName": "inherits", "rawSpec": "2", "saveSpec": null, "fetchSpec": "2"}, "_requiredBy": ["/asn1.js", "/browserify-aes", "/browserify-des", "/browserify-sign", "/browserify-sign/readable-stream", "/cipher-base", "/concat-stream", "/concat-stream/readable-stream", "/create-hash", "/create-hmac", "/crypto-browserify", "/des.js", "/duplexify", "/duplexify/readable-stream", "/elliptic", "/flush-write-stream", "/flush-write-stream/readable-stream", "/from2", "/from2/readable-stream", "/fs-write-stream-atomic/readable-stream", "/glob", "/globule/glob", "/hash-base", "/hash.js", "/hpack.js", "/hpack.js/readable-stream", "/http-errors", "/md5.js", "/memory-fs/readable-stream", "/node-libs-browser/readable-stream", "/parallel-transform", "/parallel-transform/readable-stream", "/pbkdf2/create-hash", "/pbkdf2/hash-base", "/pbkdf2/ripemd160", "/posthtml-parser/htmlparser2", "/pumpify", "/readable-stream", "/ripemd160", "/sha.js", "/sockjs-client", "/stdout-stream/readable-stream", "/stream-browserify", "/stream-browserify/readable-stream", "/stream-http", "/stream-http/readable-stream", "/through2/readable-stream", "/watchpack-chokidar2/chokidar", "/watchpack-chokidar2/readable-stream", "/webpack-dev-server/chokidar", "/webpack-dev-server/readable-stream"], "_resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "_shasum": "0fa2c64f932917c3433a0ded55363aae37416b7c", "_spec": "inherits@2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/globule/node_modules/glob", "browser": "./inherits_browser.js", "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "devDependencies": {"tap": "^14.2.4"}, "files": ["inherits.js", "inherits_browser.js"], "homepage": "https://github.com/isaacs/inherits#readme", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "license": "ISC", "main": "./inherits.js", "name": "inherits", "repository": {"type": "git", "url": "git://github.com/isaacs/inherits.git"}, "scripts": {"test": "tap"}, "version": "2.0.4"}