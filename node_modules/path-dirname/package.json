{"_from": "path-dirname@^1.0.0", "_id": "path-dirname@1.0.2", "_inBundle": false, "_integrity": "sha512-ALzNPpyNq9AqXMBjeymIjFDAkAFH06mHJH/cSBHAgU0s4vfpBn6b2nf8tiRLvagKD8RbTpq2FKTBg7cl9l3c7Q==", "_location": "/path-dirname", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "path-dirname@^1.0.0", "name": "path-dirname", "escapedName": "path-dirname", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/watchpack-chokidar2/glob-parent", "/webpack-dev-server/glob-parent"], "_resolved": "https://registry.npmjs.org/path-dirname/-/path-dirname-1.0.2.tgz", "_shasum": "cc33d24d525e099a5388c0336c6e32b9160609e0", "_spec": "path-dirname@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/watchpack-chokidar2/node_modules/glob-parent", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/es128/path-dirname/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Node.js path.dirname() ponyfill", "files": ["index.js"], "homepage": "https://github.com/es128/path-dirname#readme", "keywords": ["dirname", "dir", "path", "paths", "file", "built-in", "util", "utils", "core", "stdlib", "ponyfill", "polyfill", "shim"], "license": "MIT", "name": "path-dirname", "repository": {"type": "git", "url": "git+https://github.com/es128/path-dirname.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.2"}