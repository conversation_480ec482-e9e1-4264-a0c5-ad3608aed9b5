{"_args": [["handle-thing@2.0.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "handle-thing@2.0.1", "_id": "handle-thing@2.0.1", "_inBundle": false, "_integrity": "sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==", "_location": "/handle-thing", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "handle-thing@2.0.1", "name": "handle-thing", "escapedName": "handle-thing", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["/spdy"], "_resolved": "https://registry.npmjs.org/handle-thing/-/handle-thing-2.0.1.tgz", "_spec": "2.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/spdy-http2/handle-thing/issues"}, "description": "Wrap Streams2 instance into a HandleWrap", "devDependencies": {"istanbul": "^0.4.5", "mocha": "^5.2.0", "pre-commit": "^1.2.2", "readable-stream": "^3.0.6", "standard": "^12.0.1", "stream-pair": "^1.0.3"}, "homepage": "https://github.com/spdy-http2/handle-thing#readme", "keywords": ["handle", "net", "streams2"], "license": "MIT", "main": "lib/handle.js", "name": "handle-thing", "pre-commit": ["lint", "test"], "repository": {"type": "git", "url": "git+ssh://**************/indutny/handle-thing.git"}, "scripts": {"coverage": "istanbul cover node_modules/.bin/_mocha -- --reporter=spec test/**/*-test.js", "lint": "standard", "test": "mocha --reporter=spec test/*-test.js"}, "version": "2.0.1"}