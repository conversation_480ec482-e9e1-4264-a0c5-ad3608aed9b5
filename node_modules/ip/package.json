{"_from": "ip@^1.1.5", "_id": "ip@1.1.9", "_inBundle": false, "_integrity": "sha512-cyRxvOEpNHNtchU3Ln9KC/auJgup87llfQpQ+t5ghoC/UhL16SWzbueiCsdTnWmqAWl7LadfuwhlqmtOaqMHdQ==", "_location": "/ip", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ip@^1.1.5", "name": "ip", "escapedName": "ip", "rawSpec": "^1.1.5", "saveSpec": null, "fetchSpec": "^1.1.5"}, "_requiredBy": ["/dns-packet", "/webpack-dev-server"], "_resolved": "https://registry.npmjs.org/ip/-/ip-1.1.9.tgz", "_shasum": "8dfbcc99a754d07f425310b86a99546b1151e396", "_spec": "ip@^1.1.5", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/webpack-dev-server", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/indutny/node-ip/issues"}, "bundleDependencies": false, "deprecated": false, "description": "[![](https://badge.fury.io/js/ip.svg)](https://www.npmjs.com/package/ip)", "devDependencies": {"eslint": "^8.15.0", "mocha": "^10.0.0"}, "files": ["lib", "README.md"], "homepage": "https://github.com/indutny/node-ip", "license": "MIT", "main": "lib/ip", "name": "ip", "repository": {"type": "git", "url": "git+ssh://**************/indutny/node-ip.git"}, "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint lib/*.js test/*.js", "test": "npm run lint && mocha --reporter spec test/*-test.js"}, "version": "1.1.9"}