{"_args": [["vue-loader@13.7.3", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "vue-loader@13.7.3", "_id": "vue-loader@13.7.3", "_inBundle": false, "_integrity": "sha512-ACCwbfeC6HjY2pnDii+<PERSON><PERSON>+MZ6sdOtwvLmDXRK/BoD3WNR551V22R6KEagwHoTRJ0ZlIhpCBkptpCU6+Ri/05w==", "_location": "/vue-loader", "_phantomChildren": {"chalk": "2.4.2", "error-ex": "1.3.2", "is-directory": "0.3.1", "js-yaml": "3.7.0", "object-assign": "4.1.1", "os-homedir": "1.0.2", "postcss-load-options": "1.2.0", "postcss-load-plugins": "2.3.0", "pseudomap": "1.0.2", "require-from-string": "1.2.1"}, "_requested": {"type": "version", "registry": true, "raw": "vue-loader@13.7.3", "name": "vue-loader", "escapedName": "vue-loader", "rawSpec": "13.7.3", "saveSpec": null, "fetchSpec": "13.7.3"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/vue-loader/-/vue-loader-13.7.3.tgz", "_spec": "13.7.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/vue-loader/issues"}, "dependencies": {"consolidate": "^0.14.0", "hash-sum": "^1.0.2", "loader-utils": "^1.1.0", "lru-cache": "^4.1.1", "postcss": "^6.0.8", "postcss-load-config": "^1.1.0", "postcss-selector-parser": "^2.0.0", "prettier": "^1.7.0", "resolve": "^1.4.0", "source-map": "^0.6.1", "vue-hot-reload-api": "^2.2.0", "vue-style-loader": "^3.0.0", "vue-template-es2015-compiler": "^1.6.0"}, "description": "Vue single-file component loader for Webpack", "devDependencies": {"babel-core": "^6.25.0", "babel-loader": "^7.0.0", "babel-preset-env": "^1.6.0", "chai": "^4.1.0", "coffee-loader": "^0.7.2", "coffee-script": "^1.12.7", "css-loader": "^0.28.4", "eslint": "^3.19.0", "eslint-plugin-vue-libs": "^1.2.0", "expose-loader": "^0.7.1", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.5", "gitbook-plugin-edit-link": "^2.0.2", "gitbook-plugin-github": "^3.0.0", "gitbook-plugin-theme-vuejs": "^1.1.0", "husky": "^0.14.3", "inject-loader": "^3.0.1", "js-yaml": "^3.9.1", "jsdom": "^9.2.1", "lint-staged": "^4.0.2", "marked": "^0.3.6", "memory-fs": "^0.4.1", "mkdirp": "^0.5.1", "mocha": "^4.0.1", "node-libs-browser": "^2.0.0", "normalize-newline": "^3.0.0", "null-loader": "^0.1.1", "pug": "^2.0.0-rc.2", "raw-loader": "^0.5.1", "skeleton-loader": "1.1.3", "stylus": "^0.54.5", "stylus-loader": "^3.0.1", "sugarss": "^1.0.0", "url-loader": "^0.6.2", "vue": "^2.5.0", "vue-server-renderer": "^2.5.0", "vue-template-compiler": "^2.5.0", "webpack": "^3.7.1"}, "files": ["index.js", "lib"], "homepage": "https://github.com/vuejs/vue-loader", "keywords": ["vue", "webpack", "loader"], "license": "MIT", "lint-staged": {"lib/**/*.js": ["eslint --fix", "git add"], "test/**/*.js": ["eslint --fix", "git add"]}, "main": "index.js", "name": "vue-loader", "peerDependencies": {"css-loader": "*", "vue-template-compiler": "^2.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-loader.git"}, "scripts": {"docs": "cd docs && gitbook install && gitbook serve", "docs:deploy": "bash ./docs/deploy.sh", "lint": "eslint lib test", "lint:fix": "eslint lib test --fix", "precommit": "lint-staged", "test": "eslint lib && mocha --slow 5000 --timeout 10000"}, "version": "13.7.3"}