{"_from": "hash-base@^2.0.0", "_id": "hash-base@2.0.2", "_inBundle": false, "_integrity": "sha512-0TROgQ1/SxE6KmxWSvXHvRj90/Xo1JvZShofnYF+f6ZsGtR4eES7WfrQzPalmyagfKZCXpVnitiRebZulWsbiw==", "_location": "/pbkdf2/hash-base", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "hash-base@^2.0.0", "name": "hash-base", "escapedName": "hash-base", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/pbkdf2/ripemd160"], "_resolved": "https://registry.npmjs.org/hash-base/-/hash-base-2.0.2.tgz", "_shasum": "66ea1d856db4e8a5470cadf6fce23ae5244ef2e1", "_spec": "hash-base@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/pbkdf2/node_modules/ripemd160", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fanatid"}, "bugs": {"url": "https://github.com/crypto-browserify/hash-base/issues"}, "bundleDependencies": false, "dependencies": {"inherits": "^2.0.1"}, "deprecated": false, "description": "abstract base class for hash-streams", "devDependencies": {"nyc": "^6.1.1", "standard": "^6.0.8", "tape": "^4.2.0"}, "files": ["index.js"], "homepage": "https://github.com/crypto-browserify/hash-base", "keywords": ["hash", "stream"], "license": "MIT", "main": "index.js", "name": "hash-base", "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/hash-base.git"}, "scripts": {"coverage": "nyc node test/*.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "node test/*.js"}, "version": "2.0.2"}