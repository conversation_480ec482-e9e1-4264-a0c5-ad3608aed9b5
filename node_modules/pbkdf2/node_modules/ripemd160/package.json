{"_from": "ripemd160@=2.0.1", "_id": "ripemd160@2.0.1", "_inBundle": false, "_integrity": "sha512-J7f4wutN8mdbV08MJnXibYpCOPHR+yzy+iQ/AsjMv2j8cLavQ8VGagDFUwwTAdF8FmRKVeNpbTTEwNHCW1g94w==", "_location": "/pbkdf2/ripemd160", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ripemd160@=2.0.1", "name": "ripemd160", "escapedName": "ripemd160", "rawSpec": "=2.0.1", "saveSpec": null, "fetchSpec": "=2.0.1"}, "_requiredBy": ["/pbkdf2", "/pbkdf2/create-hash"], "_resolved": "https://registry.npmjs.org/ripemd160/-/ripemd160-2.0.1.tgz", "_shasum": "0f4584295c53a3628af7e6d79aca21ce57d1c6e7", "_spec": "ripemd160@=2.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/pbkdf2", "bugs": {"url": "https://github.com/crypto-browserify/ripemd160/issues"}, "bundleDependencies": false, "dependencies": {"hash-base": "^2.0.0", "inherits": "^2.0.1"}, "deprecated": false, "description": "Compute ripemd160 of bytes or strings.", "devDependencies": {"hash-test-vectors": "^1.3.2", "standard": "^6.0.7", "tape": "^4.5.1"}, "files": ["index.js"], "homepage": "https://github.com/crypto-browserify/ripemd160#readme", "keywords": ["string", "strings", "ripemd160", "ripe160", "bitcoin", "bytes", "cryptography"], "license": "MIT", "main": "./index", "name": "ripemd160", "repository": {"url": "git+https://github.com/crypto-browserify/ripemd160.git", "type": "git"}, "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "node test/*.js"}, "version": "2.0.1"}