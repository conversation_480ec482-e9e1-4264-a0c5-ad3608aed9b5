{"_args": [["create-hash@1.1.3", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "create-hash@1.1.3", "_id": "create-hash@1.1.3", "_inBundle": false, "_integrity": "sha512-snRpch/kwQhcdlnZKYanNF1m0RDlrCdSKQaH87w1FCFPVPNCQ/Il9QJKAX2jVBZddRdaHBMC+zXa9Gw9tmkNUA==", "_location": "/pbkdf2/create-hash", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "create-hash@1.1.3", "name": "create-hash", "escapedName": "create-hash", "rawSpec": "1.1.3", "saveSpec": null, "fetchSpec": "1.1.3"}, "_requiredBy": ["/pbkdf2"], "_resolved": "https://registry.npmjs.org/create-hash/-/create-hash-1.1.3.tgz", "_spec": "1.1.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": "", "browser": "browser.js", "bugs": {"url": "https://github.com/crypto-browserify/createHash/issues"}, "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "sha.js": "^2.4.0"}, "description": "create hashes for browserify", "devDependencies": {"hash-test-vectors": "^1.3.2", "standard": "^5.3.1", "tap-spec": "^2.1.2", "tape": "^3.0.3"}, "homepage": "https://github.com/crypto-browserify/createHash", "keywords": ["crypto"], "license": "MIT", "main": "index.js", "name": "create-hash", "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/createHash.git"}, "scripts": {"standard": "standard", "test": "npm run-script standard && npm run-script unit", "unit": "node test.js | tspec"}, "version": "1.1.3"}