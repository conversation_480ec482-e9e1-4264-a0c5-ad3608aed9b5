{"_args": [["pbkdf2@3.1.3", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "pbkdf2@3.1.3", "_id": "pbkdf2@3.1.3", "_inBundle": false, "_integrity": "sha512-wfRLBZ0feWRhCIkoMB6ete7czJcnNnqRpcoWQBLqatqXXmelSRqfdDK4F3u9T2s2cXas/hQJcryI/4lAL+XTlA==", "_location": "/pbkdf2", "_phantomChildren": {"cipher-base": "1.0.6", "inherits": "2.0.4", "sha.js": "2.4.12"}, "_requested": {"type": "version", "registry": true, "raw": "pbkdf2@3.1.3", "name": "pbkdf2", "escapedName": "pbkdf2", "rawSpec": "3.1.3", "saveSpec": null, "fetchSpec": "3.1.3"}, "_requiredBy": ["/crypto-browserify", "/parse-asn1"], "_resolved": "https://registry.npmjs.org/pbkdf2/-/pbkdf2-3.1.3.tgz", "_spec": "3.1.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "browser": {"./index.js": "./browser.js", "./lib/sync.js": "./lib/sync-browser.js"}, "bugs": {"url": "https://github.com/crypto-browserify/pbkdf2/issues"}, "dependencies": {"create-hash": "~1.1.3", "create-hmac": "^1.1.7", "ripemd160": "=2.0.1", "safe-buffer": "^5.2.1", "sha.js": "^2.4.11", "to-buffer": "^1.2.0"}, "description": "This library provides the functionality of PBKDF2 with the ability to use any supported hashing algorithm returned from crypto.getHashes()", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "benchmark": "^2.1.4", "browserify": "^17.0.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object.assign": "^4.1.7", "semver": "^6.3.1", "tape": "^5.9.0"}, "engines": {"node": ">=0.12"}, "homepage": "https://github.com/crypto-browserify/pbkdf2", "keywords": ["pbkdf2", "kdf", "salt", "hash"], "license": "MIT", "main": "index.js", "name": "pbkdf2", "publishConfig": {"ignore": [".github/workflows", "bench", "test"]}, "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/pbkdf2.git"}, "scripts": {"bench": "node bench/", "bundle-test": "browserify test/index.js > test/bundle.js", "lint": "eslint --ext=js,mjs .", "posttest": "npx npm@\">= 10.2\" audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "test": "npm run tests-only && npm run bundle-test", "tests-only": "nyc tape test/index.js", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "3.1.3"}