{"_from": "is-obj@^2.0.0", "_id": "is-obj@2.0.0", "_inBundle": false, "_integrity": "sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==", "_location": "/is-obj", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-obj@^2.0.0", "name": "is-obj", "escapedName": "is-obj", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/dot-prop"], "_resolved": "https://registry.npmjs.org/is-obj/-/is-obj-2.0.0.tgz", "_shasum": "473fb05d973705e3fd9620545018ca8e22ef4982", "_spec": "is-obj@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/dot-prop", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-obj/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if a value is an object", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/is-obj#readme", "keywords": ["object", "is", "check", "test", "type"], "license": "MIT", "name": "is-obj", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-obj.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.0.0"}