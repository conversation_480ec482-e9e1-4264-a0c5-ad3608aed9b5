{"_from": "path-key@^3.1.0", "_id": "path-key@3.1.1", "_inBundle": false, "_integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "_location": "/path-key", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "path-key@^3.1.0", "name": "path-key", "escapedName": "path-key", "rawSpec": "^3.1.0", "saveSpec": null, "fetchSpec": "^3.1.0"}, "_requiredBy": ["/cross-spawn"], "_resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "_shasum": "581f6ade658cbba65a0d3380de7753295054f375", "_spec": "path-key@^3.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/cross-spawn", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/path-key/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Get the PATH environment variable key cross-platform", "devDependencies": {"@types/node": "^11.13.0", "ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/path-key#readme", "keywords": ["path", "key", "environment", "env", "variable", "var", "get", "cross-platform", "windows"], "license": "MIT", "name": "path-key", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-key.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.1.1"}