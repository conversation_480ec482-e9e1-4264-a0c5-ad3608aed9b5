{"_from": "indexes-of@^1.0.1", "_id": "indexes-of@1.0.1", "_inBundle": false, "_integrity": "sha512-bup+4tap3Hympa+JBJUG7XuOsdNQ6fxt0MHyXMKuLBKn0OqsTfvUxkUrroEX1+B2VsSHvCjiIcZVxRtYa4nllA==", "_location": "/indexes-of", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "indexes-of@^1.0.1", "name": "indexes-of", "escapedName": "indexes-of", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/cssnano-preset-default/postcss-merge-rules/postcss-selector-parser", "/cssnano-preset-default/postcss-minify-selectors/postcss-selector-parser", "/postcss-selector-parser", "/stylehacks/postcss-selector-parser"], "_resolved": "https://registry.npmjs.org/indexes-of/-/indexes-of-1.0.1.tgz", "_shasum": "f30f716c8e2bd346c7b67d3df3915566a7c05607", "_spec": "indexes-of@^1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/postcss-selector-parser", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "bugs": {"url": "https://github.com/dominictarr/indexes-of/issues"}, "bundleDependencies": false, "deprecated": false, "description": "line String/Array#indexOf but return all the indexes in an array", "devDependencies": {"tape": "~2.1.0"}, "homepage": "https://github.com/dominictarr/indexes-of", "license": "MIT", "name": "indexes-of", "repository": {"type": "git", "url": "git://github.com/dominictarr/indexes-of.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.1"}