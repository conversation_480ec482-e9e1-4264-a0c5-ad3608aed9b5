{"_args": [["create-hash@1.2.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "create-hash@1.2.0", "_id": "create-hash@1.2.0", "_inBundle": false, "_integrity": "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==", "_location": "/create-hash", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "create-hash@1.2.0", "name": "create-hash", "escapedName": "create-hash", "rawSpec": "1.2.0", "saveSpec": null, "fetchSpec": "1.2.0"}, "_requiredBy": ["/browserify-aes", "/browserify-sign", "/create-hmac", "/crypto-browserify", "/public-encrypt"], "_resolved": "https://registry.npmjs.org/create-hash/-/create-hash-1.2.0.tgz", "_spec": "1.2.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": "", "browser": "browser.js", "bugs": {"url": "https://github.com/crypto-browserify/createHash/issues"}, "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}, "description": "create hashes for browserify", "devDependencies": {"hash-test-vectors": "^1.3.2", "safe-buffer": "^5.0.1", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3"}, "homepage": "https://github.com/crypto-browserify/createHash", "keywords": ["crypto"], "license": "MIT", "main": "index.js", "name": "create-hash", "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/createHash.git"}, "scripts": {"standard": "standard", "test": "npm run-script standard && npm run-script unit", "unit": "node test.js | tspec"}, "version": "1.2.0"}