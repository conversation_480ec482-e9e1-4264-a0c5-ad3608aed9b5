{"_args": [["postcss-convert-values@2.6.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "postcss-convert-values@2.6.1", "_id": "postcss-convert-values@2.6.1", "_inBundle": false, "_integrity": "sha512-SE7mf25D3ORUEXpu3WUqQqy0nCbMuM5BEny+ULE/FXdS/0UMA58OdzwvzuHJRpIFlk1uojt16JhaEogtP6W2oA==", "_location": "/postcss-convert-values", "_phantomChildren": {"escape-string-regexp": "1.0.5", "has-ansi": "2.0.0", "js-base64": "2.6.4", "strip-ansi": "3.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "postcss-convert-values@2.6.1", "name": "postcss-convert-values", "escapedName": "postcss-convert-values", "rawSpec": "2.6.1", "saveSpec": null, "fetchSpec": "2.6.1"}, "_requiredBy": ["/cssnano"], "_resolved": "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-2.6.1.tgz", "_spec": "2.6.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "ava": {"require": "babel-register"}, "bugs": {"url": "https://github.com/ben-eb/postcss-convert-values/issues"}, "dependencies": {"postcss": "^5.0.11", "postcss-value-parser": "^3.1.2"}, "description": "Convert values with PostCSS (e.g. ms -> s)", "devDependencies": {"all-contributors-cli": "^3.0.5", "ava": "^0.17.0", "babel-cli": "^6.3.17", "babel-core": "^6.3.26", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-es2015": "^6.3.13", "babel-preset-es2015-loose": "^7.0.0", "babel-preset-stage-0": "^6.3.13", "babel-register": "^6.9.0", "del-cli": "^0.2.0", "eslint": "^3.0.0", "eslint-config-cssnano": "^3.0.0", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-import": "^2.0.1"}, "eslintConfig": {"extends": "cssnano"}, "files": ["LICENSE-MIT", "dist"], "homepage": "https://github.com/ben-eb/postcss-convert-values", "keywords": ["css", "optimisation", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-convert-values", "repository": {"type": "git", "url": "git+https://github.com/ben-eb/postcss-convert-values.git"}, "scripts": {"contributorAdd": "all-contributors add", "contributorGenerate": "all-contributors generate", "prepublish": "del-cli dist && BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/", "pretest": "eslint src", "test": "ava", "test-012": "ava"}, "version": "2.6.1"}