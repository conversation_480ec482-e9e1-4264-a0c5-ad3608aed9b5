{"_args": [["supports-color@2.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "supports-color@2.0.0", "_id": "supports-color@2.0.0", "_inBundle": false, "_integrity": "sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==", "_location": "/postcss-convert-values/chalk/supports-color", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "supports-color@2.0.0", "name": "supports-color", "escapedName": "supports-color", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/postcss-convert-values/chalk"], "_resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "_spec": "2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "description": "Detect whether a terminal supports color", "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}, "engines": {"node": ">=0.8.0"}, "files": ["index.js"], "homepage": "https://github.com/chalk/supports-color#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}], "name": "supports-color", "repository": {"type": "git", "url": "git+https://github.com/chalk/supports-color.git"}, "scripts": {"test": "mocha"}, "version": "2.0.0"}