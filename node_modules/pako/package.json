{"_from": "pako@~1.0.5", "_id": "pako@1.0.11", "_inBundle": false, "_integrity": "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==", "_location": "/pako", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pako@~1.0.5", "name": "pako", "escapedName": "pako", "rawSpec": "~1.0.5", "saveSpec": null, "fetchSpec": "~1.0.5"}, "_requiredBy": ["/browserify-zlib"], "_resolved": "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz", "_shasum": "6c9599d340d54dfd3946380252a35705a6b992bf", "_spec": "pako@~1.0.5", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/browserify-zlib", "bugs": {"url": "https://github.com/nodeca/pako/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/andr83"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/puzrin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/dignifiedquire"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Kirill89"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}], "dependencies": {}, "deprecated": false, "description": "zlib port to javascript - fast, modularized, with browser support", "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^16.2.3", "buffer-from": "^1.1.1", "eslint": "^5.9.0", "istanbul": "^0.4.5", "mocha": "^5.2.0", "multiparty": "^4.1.3", "ndoc": "^5.0.1", "uglify-js": "=3.4.8", "zlibjs": "^0.3.1"}, "files": ["index.js", "dist/", "lib/"], "homepage": "https://github.com/nodeca/pako", "keywords": ["zlib", "deflate", "inflate", "gzip"], "license": "(MIT AND Zlib)", "name": "pako", "repository": {"type": "git", "url": "git+https://github.com/nodeca/pako.git"}, "scripts": {"test": "make test"}, "version": "1.0.11"}