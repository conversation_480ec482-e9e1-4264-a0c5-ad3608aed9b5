{"_from": "nan@^2.13.2", "_id": "nan@2.23.0", "_inBundle": false, "_integrity": "sha512-1UxuyYGdoQHcGg87Lkqm3FzefucTa0NAiOcuRsDmysep3c1LVCRK2krrUDafMWtjSG04htvAmvg96+SDknOmgQ==", "_location": "/nan", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "nan@^2.13.2", "name": "nan", "escapedName": "nan", "rawSpec": "^2.13.2", "saveSpec": null, "fetchSpec": "^2.13.2"}, "_requiredBy": ["/node-sass", "/watchpack-chokidar2/fsevents", "/webpack-dev-server/fsevents"], "_resolved": "https://registry.npmjs.org/nan/-/nan-2.23.0.tgz", "_shasum": "24aa4ddffcc37613a2d2935b97683c1ec96093c6", "_spec": "nan@^2.13.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/node-sass", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/rvagg"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kkoopa/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/trevnorris"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/TooTallNate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/brett19"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/agnat"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mkrufky"}], "deprecated": false, "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 24 compatibility", "devDependencies": {"bindings": "~1.2.1", "commander": "^2.8.1", "glob": "^5.0.14", "node-gyp": "~v10.3.1", "readable-stream": "^2.1.4", "request": "=2.81.0", "tap": "~0.7.1", "xtend": "~4.0.0"}, "homepage": "https://github.com/nodejs/nan#readme", "license": "MIT", "main": "include_dirs.js", "name": "nan", "repository": {"type": "git", "url": "git://github.com/nodejs/nan.git"}, "scripts": {"docs": "doc/.build.sh", "rebuild-tests": "node-gyp rebuild --directory test", "rebuild-tests-2015": "node-gyp rebuild --msvs_version=2015 --directory test", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js"}, "version": "2.23.0"}