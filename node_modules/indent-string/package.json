{"_from": "indent-string@^4.0.0", "_id": "indent-string@4.0.0", "_inBundle": false, "_integrity": "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==", "_location": "/indent-string", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "indent-string@^4.0.0", "name": "indent-string", "escapedName": "indent-string", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/aggregate-error", "/redent"], "_resolved": "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz", "_shasum": "624f8f4497d619b2d9768531d58f4122854d7251", "_spec": "indent-string@^4.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/redent", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/indent-string/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Indent each line in a string", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/indent-string#readme", "keywords": ["indent", "string", "pad", "align", "line", "text", "each", "every"], "license": "MIT", "name": "indent-string", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/indent-string.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.0.0"}