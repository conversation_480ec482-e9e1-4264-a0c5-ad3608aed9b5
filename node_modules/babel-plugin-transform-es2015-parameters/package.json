{"_args": [["babel-plugin-transform-es2015-parameters@6.24.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "babel-plugin-transform-es2015-parameters@6.24.1", "_id": "babel-plugin-transform-es2015-parameters@6.24.1", "_inBundle": false, "_integrity": "sha512-8HxlW+BB5HqniD+nLkQ4xSAVq3bR/pcYW9IigY+2y0dI+Y7INFeTbfAQr+63T3E4UDsZGjyb+l9txUnABWxlOQ==", "_location": "/babel-plugin-transform-es2015-parameters", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-plugin-transform-es2015-parameters@6.24.1", "name": "babel-plugin-transform-es2015-parameters", "escapedName": "babel-plugin-transform-es2015-parameters", "rawSpec": "6.24.1", "saveSpec": null, "fetchSpec": "6.24.1"}, "_requiredBy": ["/babel-preset-env"], "_resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-parameters/-/babel-plugin-transform-es2015-parameters-6.24.1.tgz", "_spec": "6.24.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "dependencies": {"babel-helper-call-delegate": "^6.24.1", "babel-helper-get-function-arity": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}, "description": "Compile ES2015 default and rest parameters to ES5", "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "babel-plugin-transform-es2015-parameters", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-es2015-parameters"}, "version": "6.24.1"}