{"_from": "log-symbols@^2.1.0", "_id": "log-symbols@2.2.0", "_inBundle": false, "_integrity": "sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg==", "_location": "/log-symbols", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "log-symbols@^2.1.0", "name": "log-symbols", "escapedName": "log-symbols", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/ora"], "_resolved": "https://registry.npmjs.org/log-symbols/-/log-symbols-2.2.0.tgz", "_shasum": "5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a", "_spec": "log-symbols@^2.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/ora", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "browser": "browser.js", "bugs": {"url": "https://github.com/sindresorhus/log-symbols/issues"}, "bundleDependencies": false, "dependencies": {"chalk": "^2.0.1"}, "deprecated": false, "description": "Colored symbols for various log levels. Example: ✔︎ Success", "devDependencies": {"ava": "*", "strip-ansi": "^4.0.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js", "browser.js"], "homepage": "https://github.com/sindresorhus/log-symbols#readme", "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "char", "symbol", "symbols", "figure", "figures", "fallback", "win", "windows", "log", "logging", "terminal", "stdout"], "license": "MIT", "name": "log-symbols", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/log-symbols.git"}, "scripts": {"test": "xo && ava"}, "version": "2.2.0"}