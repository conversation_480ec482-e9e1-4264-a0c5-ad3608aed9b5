{"_args": [["supports-color@5.5.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "supports-color@5.5.0", "_id": "supports-color@5.5.0", "_inBundle": false, "_integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "_location": "/postcss-modules-extract-imports/supports-color", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "supports-color@5.5.0", "name": "supports-color", "escapedName": "supports-color", "rawSpec": "5.5.0", "saveSpec": null, "fetchSpec": "5.5.0"}, "_requiredBy": ["/postcss-modules-extract-imports/postcss"], "_resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "_spec": "5.5.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "browser": "browser.js", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dependencies": {"has-flag": "^3.0.0"}, "description": "Detect whether a terminal supports color", "devDependencies": {"ava": "^0.25.0", "import-fresh": "^2.0.0", "xo": "^0.20.0"}, "engines": {"node": ">=4"}, "files": ["index.js", "browser.js"], "homepage": "https://github.com/chalk/supports-color#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "license": "MIT", "name": "supports-color", "repository": {"type": "git", "url": "git+https://github.com/chalk/supports-color.git"}, "scripts": {"test": "xo && ava"}, "version": "5.5.0"}