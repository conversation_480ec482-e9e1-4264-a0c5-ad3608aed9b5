{"_from": "tar@^6.1.2", "_id": "tar@6.2.1", "_inBundle": false, "_integrity": "sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==", "_location": "/tar", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "tar@^6.1.2", "name": "tar", "escapedName": "tar", "rawSpec": "^6.1.2", "saveSpec": null, "fetchSpec": "^6.1.2"}, "_requiredBy": ["/cacache", "/node-gyp"], "_resolved": "https://registry.npmjs.org/tar/-/tar-6.2.1.tgz", "_shasum": "717549c541bc3c2af15751bea94b1dd068d4b03a", "_spec": "tar@^6.1.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/node-gyp", "author": {"name": "GitHub Inc."}, "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "bundleDependencies": false, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "deprecated": false, "description": "tar for node", "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.11.0", "chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.2.9", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "engines": {"node": ">=10"}, "files": ["bin/", "lib/", "index.js"], "homepage": "https://github.com/isaacs/node-tar#readme", "license": "ISC", "name": "tar", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap"}, "tap": {"coverage-map": "map.js", "timeout": 0, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0", "content": "scripts/template-oss", "engines": ">=10", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"]}, "version": "6.2.1"}