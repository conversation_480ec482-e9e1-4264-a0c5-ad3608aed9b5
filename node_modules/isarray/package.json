{"_from": "isarray@~1.0.0", "_id": "isarray@1.0.0", "_inBundle": false, "_integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "_location": "/isarray", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "isarray@~1.0.0", "name": "isarray", "escapedName": "isarray", "rawSpec": "~1.0.0", "saveSpec": null, "fetchSpec": "~1.0.0"}, "_requiredBy": ["/browserify-sign/readable-stream", "/buffer", "/concat-stream/readable-stream", "/duplexify/readable-stream", "/flush-write-stream/readable-stream", "/from2/readable-stream", "/fs-write-stream-atomic/readable-stream", "/hpack.js/readable-stream", "/memory-fs/readable-stream", "/node-libs-browser/readable-stream", "/parallel-transform/readable-stream", "/posthtml-parser/isobject", "/stdout-stream/readable-stream", "/stream-browserify/readable-stream", "/stream-http/readable-stream", "/through2/readable-stream", "/unset-value/has-value/isobject", "/watchpack-chokidar2/readable-stream", "/webpack-dev-server/readable-stream"], "_resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "_shasum": "bb935d48582cba168c06834957a54a3e07124f11", "_spec": "isarray@~1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/stdout-stream/node_modules/readable-stream", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Array#isArray for older browsers", "devDependencies": {"tape": "~2.13.4"}, "homepage": "https://github.com/juliangruber/isarray", "keywords": ["browser", "isarray", "array"], "license": "MIT", "main": "index.js", "name": "isarray", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "scripts": {"test": "tape test.js"}, "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "1.0.0"}