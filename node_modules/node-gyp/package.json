{"_args": [["node-gyp@8.4.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "node-gyp@8.4.1", "_id": "node-gyp@8.4.1", "_inBundle": false, "_integrity": "sha512-olTJRgUtAb/hOXG0E93wZDs5YiJlgbXxTwQAFHyNlRsXQnYzUaF2aGgujZbw+hR8aF4ZG/rST57bWMWD16jr9w==", "_location": "/node-gyp", "_phantomChildren": {"are-we-there-yet": "3.0.1", "console-control-strings": "1.1.0", "gauge": "4.0.4", "glob": "7.2.3", "set-blocking": "2.0.0"}, "_requested": {"type": "version", "registry": true, "raw": "node-gyp@8.4.1", "name": "node-gyp", "escapedName": "node-gyp", "rawSpec": "8.4.1", "saveSpec": null, "fetchSpec": "8.4.1"}, "_requiredBy": ["/node-sass"], "_resolved": "https://registry.npmjs.org/node-gyp/-/node-gyp-8.4.1.tgz", "_spec": "8.4.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tootallnate.net"}, "bin": {"node-gyp": "bin/node-gyp.js"}, "bugs": {"url": "https://github.com/nodejs/node-gyp/issues"}, "dependencies": {"env-paths": "^2.2.0", "glob": "^7.1.4", "graceful-fs": "^4.2.6", "make-fetch-happen": "^9.1.0", "nopt": "^5.0.0", "npmlog": "^6.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.2", "which": "^2.0.2"}, "description": "Node.js native addon build tool", "devDependencies": {"bindings": "^1.5.0", "nan": "^2.14.2", "require-inject": "^1.4.4", "standard": "^14.3.4", "tap": "^12.7.0"}, "engines": {"node": ">= 10.12.0"}, "homepage": "https://github.com/nodejs/node-gyp#readme", "installVersion": 9, "keywords": ["native", "addon", "module", "c", "c++", "bindings", "gyp"], "license": "MIT", "main": "./lib/node-gyp.js", "name": "node-gyp", "preferGlobal": true, "repository": {"type": "git", "url": "git://github.com/nodejs/node-gyp.git"}, "scripts": {"lint": "standard */*.js test/**/*.js", "test": "npm run lint && tap --timeout=120 test/test-*"}, "version": "8.4.1"}