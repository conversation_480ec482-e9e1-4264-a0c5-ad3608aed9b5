{"_args": [["npmlog@6.0.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "npmlog@6.0.2", "_id": "npmlog@6.0.2", "_inBundle": false, "_integrity": "sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg==", "_location": "/node-gyp/npmlog", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "npmlog@6.0.2", "name": "npmlog", "escapedName": "npmlog", "rawSpec": "6.0.2", "saveSpec": null, "fetchSpec": "6.0.2"}, "_requiredBy": ["/node-gyp"], "_resolved": "https://registry.npmjs.org/npmlog/-/npmlog-6.0.2.tgz", "_spec": "6.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "GitHub Inc."}, "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dependencies": {"are-we-there-yet": "^3.0.0", "console-control-strings": "^1.1.0", "gauge": "^4.0.3", "set-blocking": "^2.0.0"}, "description": "logger for npm", "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.4.1", "tap": "^16.0.1"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "files": ["bin/", "lib/"], "homepage": "https://github.com/npm/npmlog#readme", "license": "ISC", "main": "lib/log.js", "name": "npmlog", "repository": {"type": "git", "url": "git+https://github.com/npm/npmlog.git"}, "scripts": {"lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "npmclilint": "npmcli-lint", "postlint": "template-oss-check", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "snap": "tap", "template-oss-apply": "template-oss-apply --force", "test": "tap"}, "tap": {"branches": 95}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.4.1"}, "version": "6.0.2"}