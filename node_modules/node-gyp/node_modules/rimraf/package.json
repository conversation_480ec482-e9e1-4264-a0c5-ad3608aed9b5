{"_args": [["rimraf@3.0.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "rimraf@3.0.2", "_id": "rimraf@3.0.2", "_inBundle": false, "_integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "_location": "/node-gyp/rimraf", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "rimraf@3.0.2", "name": "<PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON>", "rawSpec": "3.0.2", "saveSpec": null, "fetchSpec": "3.0.2"}, "_requiredBy": ["/node-gyp"], "_resolved": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "_spec": "3.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bin": {"rimraf": "bin.js"}, "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "dependencies": {"glob": "^7.1.3"}, "description": "A deep deletion module for node (like `rm -rf`)", "devDependencies": {"mkdirp": "^0.5.1", "tap": "^12.1.1"}, "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "funding": {"url": "https://github.com/sponsors/isaacs"}, "homepage": "https://github.com/isaacs/rimraf#readme", "license": "ISC", "main": "rimraf.js", "name": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/isaacs/rimraf.git"}, "scripts": {"postpublish": "git push origin --follow-tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js"}, "version": "3.0.2"}