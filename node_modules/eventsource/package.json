{"_args": [["eventsource@0.1.6", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "eventsource@0.1.6", "_id": "eventsource@0.1.6", "_inBundle": false, "_integrity": "sha512-bbB5tEuvC+SuRUG64X8ghvjgiRniuA4WlehWbFnoN4z6TxDXpyX+BMHF7rMgZAqoe+EbyNRUbHN0uuP9phy5jQ==", "_location": "/eventsource", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "eventsource@0.1.6", "name": "eventsource", "escapedName": "eventsource", "rawSpec": "0.1.6", "saveSpec": null, "fetchSpec": "0.1.6"}, "_requiredBy": ["/sockjs-client"], "_resolved": "https://registry.npmjs.org/eventsource/-/eventsource-0.1.6.tgz", "_spec": "0.1.6", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "http://github.com/aslakhellesoy/eventsource-node/issues"}, "dependencies": {"original": ">=0.0.5"}, "description": "W3C compliant EventSource client for Node.js", "devDependencies": {"mocha": ">=1.21.4"}, "directories": {"lib": "./lib"}, "engines": {"node": ">=0.8.0"}, "homepage": "http://github.com/aslakhellesoy/eventsource-node", "keywords": ["eventsource", "http", "streaming", "sse"], "licenses": [{"type": "MIT", "url": "http://github.com/aslakhellesoy/eventsource-node/raw/master/LICENSE"}], "main": "./lib/eventsource", "name": "eventsource", "repository": {"type": "git", "url": "git://github.com/aslakhellesoy/eventsource-node.git"}, "scripts": {"postpublish": "git push && git push --tags", "test": "mocha --reporter spec"}, "version": "0.1.6"}