{"_args": [["babylon@6.18.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "babylon@6.18.0", "_id": "babylon@6.18.0", "_inBundle": false, "_integrity": "sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ==", "_location": "/babylon", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babylon@6.18.0", "name": "babylon", "escapedName": "babylon", "rawSpec": "6.18.0", "saveSpec": null, "fetchSpec": "6.18.0"}, "_requiredBy": ["/babel-core", "/babel-template", "/babel-traverse"], "_resolved": "https://registry.npmjs.org/babylon/-/babylon-6.18.0.tgz", "_spec": "6.18.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "ava": {"files": ["test/*.js"], "source": ["src/**/*.js", "bin/**/*.js"]}, "bin": {"babylon": "bin/babylon.js"}, "bugs": {"url": "https://github.com/babel/babylon/issues"}, "description": "A JavaScript parser", "devDependencies": {"ava": "^0.17.0", "babel-cli": "^6.14.0", "babel-eslint": "^7.0.0", "babel-helper-fixtures": "^6.9.0", "babel-plugin-external-helpers": "^6.18.0", "babel-plugin-istanbul": "^3.0.0", "babel-plugin-transform-flow-strip-types": "^6.14.0", "babel-preset-es2015": "^6.14.0", "babel-preset-stage-0": "^6.5.0", "chalk": "^1.1.3", "codecov": "^1.0.1", "cross-env": "^2.0.0", "eslint": "^3.7.1", "eslint-config-babel": "^6.0.0", "eslint-plugin-flowtype": "^2.20.0", "flow-bin": "^0.42.0", "nyc": "^10.0.0", "rimraf": "^2.5.4", "rollup": "^0.41.0", "rollup-plugin-babel": "^2.6.1", "rollup-plugin-node-resolve": "^2.0.0", "rollup-watch": "^3.2.2", "unicode-9.0.0": "~0.7.0"}, "files": ["bin", "lib"], "greenkeeper": {"ignore": ["cross-env"]}, "homepage": "https://babeljs.io/", "keywords": ["babel", "javascript", "parser", "babylon"], "license": "MIT", "main": "lib/index.js", "name": "babylon", "nyc": {"include": ["src/**/*.js", "bin/**/*.js"], "sourceMap": false, "instrument": false}, "repository": {"type": "git", "url": "git+https://github.com/babel/babylon.git"}, "scripts": {"build": "npm run clean && rollup -c", "changelog": "git log `git describe --tags --abbrev=0`..HEAD --pretty=format:' * %s (%an)' | grep -v 'Merge pull request'", "clean": "<PERSON><PERSON><PERSON> lib", "coverage": "nyc report --reporter=json && codecov -f coverage/coverage-final.json", "flow": "flow", "lint": "eslint src bin", "prepublish": "cross-env BABEL_ENV=production npm run build", "preversion": "npm run test && npm run changelog", "test": "npm run lint && npm run flow && npm run build -- -m && npm run test-only", "test-ci": "nyc npm run test-only", "test-only": "ava", "watch": "npm run clean && rollup -c --watch"}, "version": "6.18.0"}