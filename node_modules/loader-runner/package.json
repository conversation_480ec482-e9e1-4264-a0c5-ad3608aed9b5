{"_args": [["loader-runner@2.4.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "loader-runner@2.4.0", "_id": "loader-runner@2.4.0", "_inBundle": false, "_integrity": "sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw==", "_location": "/loader-runner", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "loader-runner@2.4.0", "name": "loader-runner", "escapedName": "loader-runner", "rawSpec": "2.4.0", "saveSpec": null, "fetchSpec": "2.4.0"}, "_requiredBy": ["/webpack"], "_resolved": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.4.0.tgz", "_spec": "2.4.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack/loader-runner/issues"}, "description": "Runs (webpack) loaders", "devDependencies": {"codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}, "files": ["lib/", "bin/", "hot/", "web_modules/", "schemas/"], "homepage": "https://github.com/webpack/loader-runner#readme", "keywords": ["webpack", "loader"], "license": "MIT", "main": "lib/LoaderRunner.js", "name": "loader-runner", "repository": {"type": "git", "url": "git+https://github.com/webpack/loader-runner.git"}, "scripts": {"cover": "istanbul cover node_modules/mocha/bin/_mocha", "lint": "eslint lib test", "precover": "npm run lint", "pretest": "npm run lint", "test": "mocha --reporter spec", "travis": "npm run cover -- --report lcovonly"}, "version": "2.4.0"}