{"_args": [["hosted-git-info@2.8.9", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "hosted-git-info@2.8.9", "_id": "hosted-git-info@2.8.9", "_inBundle": false, "_integrity": "sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==", "_location": "/webpack/hosted-git-info", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "hosted-git-info@2.8.9", "name": "hosted-git-info", "escapedName": "hosted-git-info", "rawSpec": "2.8.9", "saveSpec": null, "fetchSpec": "2.8.9"}, "_requiredBy": ["/webpack/normalize-package-data"], "_resolved": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.9.tgz", "_spec": "2.8.9", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "bugs": {"url": "https://github.com/npm/hosted-git-info/issues"}, "description": "Provides metadata and conversions from repository urls for Github, Bitbucket and Gitlab", "devDependencies": {"standard": "^11.0.1", "standard-version": "^4.4.0", "tap": "^12.7.0"}, "files": ["index.js", "git-host.js", "git-host-info.js"], "homepage": "https://github.com/npm/hosted-git-info", "keywords": ["git", "github", "bitbucket", "gitlab"], "license": "ISC", "main": "index.js", "name": "hosted-git-info", "repository": {"type": "git", "url": "git+https://github.com/npm/hosted-git-info.git"}, "scripts": {"postrelease": "npm publish --tag=ancient-legacy-fixes && git push --follow-tags", "posttest": "standard", "prerelease": "npm t", "release": "standard-version -s", "test": "tap -J --coverage=90 --no-esm test/*.js", "test:coverage": "tap --coverage-report=html -J --coverage=90 --no-esm test/*.js"}, "version": "2.8.9"}