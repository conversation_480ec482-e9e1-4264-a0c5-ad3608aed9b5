{"_args": [["camelcase@4.1.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "camelcase@4.1.0", "_id": "camelcase@4.1.0", "_inBundle": false, "_integrity": "sha512-FxAv7HpHrXbh3aPo4o2qxHay2lkLY3x5Mw3KeE4KQE8ysVfziWeRZDwcjauvwBSGEC/nXUPzZy8zeh4HokqOnw==", "_location": "/webpack/yargs/camelcase", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "camelcase@4.1.0", "name": "camelcase", "escapedName": "camelcase", "rawSpec": "4.1.0", "saveSpec": null, "fetchSpec": "4.1.0"}, "_requiredBy": ["/webpack/yargs"], "_resolved": "https://registry.npmjs.org/camelcase/-/camelcase-4.1.0.tgz", "_spec": "4.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/camelcase#readme", "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "license": "MIT", "name": "camelcase", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "scripts": {"test": "xo && ava"}, "version": "4.1.0", "xo": {"esnext": true}}