{"_args": [["p-try@1.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "p-try@1.0.0", "_id": "p-try@1.0.0", "_inBundle": false, "_integrity": "sha512-U1etNYuMJoIz3ZXSrrySFjsXQTWOx2/jdi86L+2pRvph/qMKL6sbcCYdH23fqsbm8TH2Gn0OybpT4eSFlCVHww==", "_location": "/webpack/p-try", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "p-try@1.0.0", "name": "p-try", "escapedName": "p-try", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/webpack/p-limit"], "_resolved": "https://registry.npmjs.org/p-try/-/p-try-1.0.0.tgz", "_spec": "1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-try/issues"}, "description": "`Promise#try()` ponyfill - Starts a promise chain", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/p-try#readme", "keywords": ["promise", "try", "resolve", "function", "catch", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "license": "MIT", "name": "p-try", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-try.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.0", "xo": {"esnext": true}}