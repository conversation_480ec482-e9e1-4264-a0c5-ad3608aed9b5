{"_from": "bn.js@^4.0.0", "_id": "bn.js@4.12.2", "_inBundle": false, "_integrity": "sha512-n4DSx829VRTRByMRGdjQ9iqsN0Bh4OolPsFnaZBLcbi8iXcB+kJ9s7EnRt4wILZNV3kPLHkRVfOc/HvhC3ovDw==", "_location": "/miller-rabin/bn.js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "bn.js@^4.0.0", "name": "bn.js", "escapedName": "bn.js", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/miller-rabin"], "_resolved": "https://registry.npmjs.org/bn.js/-/bn.js-4.12.2.tgz", "_shasum": "3d8fed6796c24e177737f7cc5172ee04ef39ec99", "_spec": "bn.js@^4.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/miller-rabin", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": {"buffer": false}, "bugs": {"url": "https://github.com/indutny/bn.js/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Big number implementation in pure javascript", "devDependencies": {"istanbul": "^0.3.5", "mocha": "^2.1.0", "semistandard": "^7.0.4"}, "homepage": "https://github.com/indutny/bn.js", "keywords": ["BN", "BigNum", "Big number", "<PERSON><PERSON><PERSON>", "<PERSON>"], "license": "MIT", "main": "lib/bn.js", "name": "bn.js", "repository": {"type": "git", "url": "git+ssh://**************/indutny/bn.js.git"}, "scripts": {"lint": "semistandard", "test": "npm run lint && npm run unit", "unit": "mocha --reporter=spec test/*-test.js"}, "version": "4.12.2"}