{"_from": "is-negative-zero@^2.0.3", "_id": "is-negative-zero@2.0.3", "_inBundle": false, "_integrity": "sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==", "_location": "/is-negative-zero", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-negative-zero@^2.0.3", "name": "is-negative-zero", "escapedName": "is-negative-zero", "rawSpec": "^2.0.3", "saveSpec": null, "fetchSpec": "^2.0.3"}, "_requiredBy": ["/es-abstract"], "_resolved": "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.3.tgz", "_shasum": "ced903a027aca6381b777a5743069d7376a49747", "_spec": "is-negative-zero@^2.0.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-negative-zero/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Is this value negative zero? === will lie to you", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-negative-zero", "keywords": ["is", "negative", "zero", "negative zero", "number", "positive", "0", "-0"], "license": "MIT", "main": "index.js", "name": "is-negative-zero", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-negative-zero.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "types": "./index.d.ts", "version": "2.0.3"}