{"_from": "is-buffer@^2.0.2", "_id": "is-buffer@2.0.5", "_inBundle": false, "_integrity": "sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==", "_location": "/is-buffer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-buffer@^2.0.2", "name": "is-buffer", "escapedName": "is-buffer", "rawSpec": "^2.0.2", "saveSpec": null, "fetchSpec": "^2.0.2"}, "_requiredBy": ["/axios"], "_resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-2.0.5.tgz", "_shasum": "ebc252e400d22ff8d77fa09888821a24a658c191", "_spec": "is-buffer@^2.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/axios", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/is-buffer/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Determine if an object is a Buffer", "devDependencies": {"airtap": "^3.0.0", "standard": "*", "tape": "^5.0.1"}, "engines": {"node": ">=4"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "homepage": "https://github.com/feross/is-buffer#readme", "keywords": ["arraybuffer", "browser", "browser buffer", "browserify", "buffer", "buffers", "core buffer", "dataview", "float32array", "float64array", "int16array", "int32array", "type", "typed array", "uint32array"], "license": "MIT", "main": "index.js", "name": "is-buffer", "repository": {"type": "git", "url": "git://github.com/feross/is-buffer.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "version": "2.0.5"}