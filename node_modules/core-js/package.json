{"_args": [["core-js@2.5.5", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "core-js@2.5.5", "_id": "core-js@2.5.5", "_inBundle": false, "_integrity": "sha1-sU3ek2xkDAV5prUMq8wTLdYSfjs=", "_location": "/core-js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "core-js@2.5.5", "name": "core-js", "escapedName": "core-js", "rawSpec": "2.5.5", "saveSpec": null, "fetchSpec": "2.5.5"}, "_requiredBy": ["/babel-register", "/babel-runtime"], "_resolved": "https://registry.npmjs.org/core-js/-/core-js-2.5.5.tgz", "_spec": "2.5.5", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "description": "Standard library", "devDependencies": {"LiveScript": "1.3.x", "es-observable-tests": "0.2.x", "eslint": "4.19.x", "eslint-plugin-import": "2.10.x", "grunt": "^1.0.2", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-uglify": "3.3.x", "grunt-contrib-watch": "^1.0.0", "grunt-karma": "^2.0.0", "grunt-livescript": "0.6.x", "karma": "^2.0.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "karma-ie-launcher": "^1.0.0", "karma-phantomjs-launcher": "1.0.x", "karma-qunit": "^2.0.1", "phantomjs-prebuilt": "2.1.x", "promises-aplus-tests": "^2.1.2", "qunit": "2.6.x", "temp": "^0.8.3", "webpack": "^3.11.0"}, "homepage": "https://github.com/zloirock/core-js#readme", "keywords": ["ES3", "ES5", "ES6", "ES7", "ES2015", "ES2016", "ES2017", "ECMAScript 3", "ECMAScript 5", "ECMAScript 6", "ECMAScript 7", "ECMAScript 2015", "ECMAScript 2016", "ECMAScript 2017", "Harmony", "<PERSON><PERSON><PERSON>", "Map", "Set", "WeakMap", "WeakSet", "Promise", "Symbol", "TypedArray", "setImmediate", "Dict", "polyfill", "shim"], "license": "MIT", "main": "index.js", "name": "core-js", "repository": {"type": "git", "url": "git+https://github.com/zloirock/core-js.git"}, "scripts": {"grunt": "grunt", "lint": "eslint ./", "observables-tests": "node tests/observables/adapter && node tests/observables/adapter-library", "promises-tests": "promises-aplus-tests tests/promises-aplus/adapter", "test": "npm run grunt clean copy && npm run lint && npm run grunt livescript client karma:default && npm run grunt library karma:library && npm run promises-tests && npm run observables-tests && lsc tests/commonjs"}, "version": "2.5.5"}