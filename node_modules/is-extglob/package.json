{"_from": "is-extglob@^2.1.1", "_id": "is-extglob@2.1.1", "_inBundle": false, "_integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "_location": "/is-extglob", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-extglob@^2.1.1", "name": "is-extglob", "escapedName": "is-extglob", "rawSpec": "^2.1.1", "saveSpec": null, "fetchSpec": "^2.1.1"}, "_requiredBy": ["/is-glob", "/watchpack-chokidar2/glob-parent/is-glob", "/webpack-dev-server/glob-parent/is-glob"], "_resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "_shasum": "a88c02535791f02ed37c76a1b9ea9773c833f8c2", "_spec": "is-extglob@^2.1.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/is-glob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-extglob/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Returns true if a string has an extglob.", "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-extglob", "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "license": "MIT", "main": "index.js", "name": "is-extglob", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-extglob.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["has-glob", "is-glob", "micromatch"]}, "reflinks": ["verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "version": "2.1.1"}