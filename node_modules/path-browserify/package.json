{"_from": "path-browserify@0.0.1", "_id": "path-browserify@0.0.1", "_inBundle": false, "_integrity": "sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ==", "_location": "/path-browserify", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "path-browserify@0.0.1", "name": "path-browserify", "escapedName": "path-browserify", "rawSpec": "0.0.1", "saveSpec": null, "fetchSpec": "0.0.1"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://registry.npmjs.org/path-browserify/-/path-browserify-0.0.1.tgz", "_shasum": "e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a", "_spec": "path-browserify@0.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/node-libs-browser", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/path-browserify/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "the path module from node core for browsers", "devDependencies": {"tape": "~1.0.4"}, "homepage": "https://github.com/substack/path-browserify", "keywords": ["path", "browser", "browserify"], "license": "MIT", "main": "index.js", "name": "path-browserify", "repository": {"type": "git", "url": "git://github.com/substack/path-browserify.git"}, "scripts": {"test": "node test/test-path.js"}, "version": "0.0.1"}