{"_from": "mimic-fn@^1.0.0", "_id": "mimic-fn@1.2.0", "_inBundle": false, "_integrity": "sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==", "_location": "/mimic-fn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mimic-fn@^1.0.0", "name": "mimic-fn", "escapedName": "mimic-fn", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/mem", "/onetime"], "_resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz", "_shasum": "820c86a39334640e99516928bd03fca88057d022", "_spec": "mimic-fn@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/onetime", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Make a function mimic another one", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "license": "MIT", "name": "mimic-fn", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "scripts": {"test": "xo && ava"}, "version": "1.2.0"}