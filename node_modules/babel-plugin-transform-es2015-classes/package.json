{"_args": [["babel-plugin-transform-es2015-classes@6.24.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "babel-plugin-transform-es2015-classes@6.24.1", "_id": "babel-plugin-transform-es2015-classes@6.24.1", "_inBundle": false, "_integrity": "sha512-5Dy7ZbRinGrNtmWpquZKZ3EGY8sDgIVB4CU8Om8q8tnMLrD/m94cKglVcHps0BCTdZ0TJeeAWOq2TK9MIY6cag==", "_location": "/babel-plugin-transform-es2015-classes", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-plugin-transform-es2015-classes@6.24.1", "name": "babel-plugin-transform-es2015-classes", "escapedName": "babel-plugin-transform-es2015-classes", "rawSpec": "6.24.1", "saveSpec": null, "fetchSpec": "6.24.1"}, "_requiredBy": ["/babel-preset-env"], "_resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-classes/-/babel-plugin-transform-es2015-classes-6.24.1.tgz", "_spec": "6.24.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "dependencies": {"babel-helper-define-map": "^6.24.1", "babel-helper-function-name": "^6.24.1", "babel-helper-optimise-call-expression": "^6.24.1", "babel-helper-replace-supers": "^6.24.1", "babel-messages": "^6.23.0", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}, "description": "Compile ES2015 classes to ES5", "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "babel-plugin-transform-es2015-classes", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-es2015-classes"}, "version": "6.24.1"}