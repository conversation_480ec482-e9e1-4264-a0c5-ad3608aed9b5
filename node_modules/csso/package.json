{"_args": [["csso@2.3.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "csso@2.3.2", "_id": "csso@2.3.2", "_inBundle": false, "_integrity": "sha512-FmCI/hmqDeHHLaIQckMhMZneS84yzUZdrWDAvJVVxOwcKE1P1LF9FGmzr1ktIQSxOw6fl3PaQsmfg+GN+VvR3w==", "_location": "/csso", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "csso@2.3.2", "name": "csso", "escapedName": "csso", "rawSpec": "2.3.2", "saveSpec": null, "fetchSpec": "2.3.2"}, "_requiredBy": ["/svgo"], "_resolved": "https://registry.npmjs.org/csso/-/csso-2.3.2.tgz", "_spec": "2.3.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@ya.ru", "url": "https://github.com/afelix"}, "bin": {"csso": "bin/csso"}, "bugs": {"url": "https://github.com/css/csso/issues"}, "dependencies": {"clap": "^1.0.9", "source-map": "^0.5.3"}, "description": "CSSO (CSS Optimizer) is a CSS minifier with structural optimisations", "devDependencies": {"browserify": "^13.0.0", "coveralls": "^2.11.6", "eslint": "^2.2.0", "istanbul": "^0.4.2", "jscs": "~2.10.0", "mocha": "~2.4.2", "uglify-js": "^2.6.1"}, "engines": {"node": ">=0.10.0"}, "eslintConfig": {"env": {"node": true, "mocha": true, "es6": true}, "rules": {"no-duplicate-case": 2, "no-undef": 2, "no-unused-vars": [2, {"vars": "all", "args": "after-used"}]}}, "files": ["bin", "dist/csso-browser.js", "lib", "HISTORY.md", "LICENSE", "README.md"], "homepage": "https://github.com/css/csso", "keywords": ["css", "minifier", "minify", "compress", "optimisation"], "license": "MIT", "main": "./lib/index", "maintainers": [{"name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}], "name": "csso", "repository": {"type": "git", "url": "git+https://github.com/css/csso.git"}, "scripts": {"browserify": "browserify --standalone csso lib/index.js | uglifyjs --compress --mangle -o dist/csso-browser.js", "codestyle": "jscs lib && eslint lib test", "codestyle-and-test": "npm run codestyle && npm test", "coverage": "istanbul cover _mocha -- -R dot", "coveralls": "istanbul cover _mocha --report lcovonly -- -R dot && cat ./coverage/lcov.info | coveralls", "gh-pages": "git clone -b gh-pages https://github.com/css/csso.git .gh-pages && npm run browserify && cp dist/csso-browser.js .gh-pages/ && cd .gh-pages && git commit -am \"update\" && git push && cd .. && rm -rf .gh-pages", "hydrogen": "node --trace-hydrogen --trace-phase=Z --trace-deopt --code-comments --hydrogen-track-positions --redirect-code-traces --redirect-code-traces-to=code.asm --trace_hydrogen_file=code.cfg --print-opt-code bin/csso --stat -o /dev/null", "prepublish": "npm run browserify", "test": "mocha --reporter dot", "travis": "npm run codestyle-and-test && npm run coveralls"}, "version": "2.3.2"}