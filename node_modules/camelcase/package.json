{"_from": "camelcase@^5.3.1", "_id": "camelcase@5.3.1", "_inBundle": false, "_integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==", "_location": "/camelcase", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "camelcase@^5.3.1", "name": "camelcase", "escapedName": "camelcase", "rawSpec": "^5.3.1", "saveSpec": null, "fetchSpec": "^5.3.1"}, "_requiredBy": ["/camelcase-keys"], "_resolved": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "_shasum": "e3c9b31569e106811df242f715725a1f4c494320", "_spec": "camelcase@^5.3.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/camelcase-keys", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/camelcase#readme", "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "license": "MIT", "name": "camelcase", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "5.3.1"}