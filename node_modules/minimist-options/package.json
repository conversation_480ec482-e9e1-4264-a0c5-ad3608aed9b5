{"_from": "minimist-options@4.1.0", "_id": "minimist-options@4.1.0", "_inBundle": false, "_integrity": "sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==", "_location": "/minimist-options", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "minimist-options@4.1.0", "name": "minimist-options", "escapedName": "minimist-options", "rawSpec": "4.1.0", "saveSpec": null, "fetchSpec": "4.1.0"}, "_requiredBy": ["/meow"], "_resolved": "https://registry.npmjs.org/minimist-options/-/minimist-options-4.1.0.tgz", "_shasum": "c0655713c53a8a2ebd77ffa247d342c40f010619", "_spec": "minimist-options@4.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/meow", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/vadimdemedes/minimist-options/issues"}, "bundleDependencies": false, "dependencies": {"arrify": "^1.0.1", "is-plain-obj": "^1.1.0", "kind-of": "^6.0.3"}, "deprecated": false, "description": "Pretty options for minimist", "devDependencies": {"@types/minimist": "^1.2.0", "ava": "^1.0.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "engines": {"node": ">= 6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/vadimdemedes/minimist-options#readme", "keywords": ["minimist", "argv", "args"], "license": "MIT", "name": "minimist-options", "repository": {"type": "git", "url": "git+https://github.com/vadimdemedes/minimist-options.git"}, "scripts": {"test": "xo && ava && tsd-check"}, "version": "4.1.0"}