{"_args": [["fsevents@2.3.3", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "fsevents@2.3.3", "_id": "fsevents@2.3.3", "_inBundle": false, "_integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "_location": "/fsevents", "_optional": true, "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "fsevents@2.3.3", "name": "fsevents", "escapedName": "fsevents", "rawSpec": "2.3.3", "saveSpec": null, "fetchSpec": "2.3.3"}, "_requiredBy": ["/chokidar"], "_resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "_spec": "2.3.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "description": "Native Access to MacOS FSEvents", "devDependencies": {"node-gyp": "^9.4.0"}, "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "files": ["fsevents.d.ts", "fsevents.js", "fsevents.node"], "homepage": "https://github.com/fsevents/fsevents", "keywords": ["fsevents", "mac"], "license": "MIT", "main": "fsevents.js", "name": "fsevents", "os": ["darwin"], "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "scripts": {"build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "clean": "node-gyp clean && rm -f fsevents.node", "prepublishOnly": "npm run build", "test": "/bin/bash ./test.sh 2>/dev/null"}, "types": "fsevents.d.ts", "version": "2.3.3"}