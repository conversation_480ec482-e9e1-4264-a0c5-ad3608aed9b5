{"_from": "math-expression-evaluator@^1.2.14", "_id": "math-expression-evaluator@1.4.0", "_inBundle": false, "_integrity": "sha512-4vRUvPyxdO8cWULGTh9dZWL2tZK6LDBvj+OGHBER7poH9Qdt7kXEoj20wiz4lQUbUXQZFjPbe5mVDo9nutizCw==", "_location": "/math-expression-evaluator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "math-expression-evaluator@^1.2.14", "name": "math-expression-evaluator", "escapedName": "math-expression-evaluator", "rawSpec": "^1.2.14", "saveSpec": null, "fetchSpec": "^1.2.14"}, "_requiredBy": ["/reduce-css-calc"], "_resolved": "https://registry.npmjs.org/math-expression-evaluator/-/math-expression-evaluator-1.4.0.tgz", "_shasum": "3d66031117fbb7b9715ea6c9c68c2cd2eebd37e2", "_spec": "math-expression-evaluator@^1.2.14", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/reduce-css-calc", "author": {"name": "Ankit", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/redhivesoftware/math-expression-evaluator/issues"}, "bundleDependencies": false, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "deprecated": false, "description": "A flexible math expression evaluator", "devDependencies": {"cz-conventional-changelog": "^3.3.0", "eslint": "^6.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.2.0", "eslint-plugin-node": "^4.2.2", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "husky": "^7.0.4", "mocha": "^2.2.5", "semantic-release": "^19.0.2", "webpack": "^5.67.0", "webpack-cli": "^4.9.2"}, "files": ["src", "dist"], "homepage": "https://github.com/redhivesoftware/math-expression-evaluator#readme", "keywords": ["math", "expression", "evaluator", "parser"], "license": "MIT", "main": "src/formula_evaluator.js", "name": "math-expression-evaluator", "repository": {"type": "git", "url": "git+https://github.com/redhivesoftware/math-expression-evaluator.git"}, "scripts": {"build": "webpack && NODE_ENV=production webpack", "prepare": "husky install", "test": "mocha"}, "version": "1.4.0"}