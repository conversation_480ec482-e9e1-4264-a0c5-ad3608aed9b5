{"_from": "http-cache-semantics@^4.1.0", "_id": "http-cache-semantics@4.2.0", "_inBundle": false, "_integrity": "sha512-dTxcvPXqPvXBQpq5dUr6mEMJX4oIEFv6bwom3FDwKRDsuIjjJGANqhBuoAn9c1RQJIdAKav33ED65E2ys+87QQ==", "_location": "/http-cache-semantics", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "http-cache-semantics@^4.1.0", "name": "http-cache-semantics", "escapedName": "http-cache-semantics", "rawSpec": "^4.1.0", "saveSpec": null, "fetchSpec": "^4.1.0"}, "_requiredBy": ["/make-fetch-happen"], "_resolved": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.2.0.tgz", "_shasum": "205f4db64f8562b76a4ff9235aa5279839a09dd5", "_spec": "http-cache-semantics@^4.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/make-fetch-happen", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "bugs": {"url": "https://github.com/kornelski/http-cache-semantics/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "devDependencies": {"mocha": "^11.0"}, "files": ["index.js"], "homepage": "https://github.com/kornelski/http-cache-semantics#readme", "license": "BSD-2-<PERSON><PERSON>", "main": "index.js", "name": "http-cache-semantics", "repository": {"type": "git", "url": "git+https://github.com/kornelski/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "types": "index.js", "version": "4.2.0"}