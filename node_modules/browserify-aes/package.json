{"_args": [["browserify-aes@1.2.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "browserify-aes@1.2.0", "_id": "browserify-aes@1.2.0", "_inBundle": false, "_integrity": "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==", "_location": "/browserify-aes", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "browserify-aes@1.2.0", "name": "browserify-aes", "escapedName": "browserify-aes", "rawSpec": "1.2.0", "saveSpec": null, "fetchSpec": "1.2.0"}, "_requiredBy": ["/browserify-cipher", "/parse-asn1"], "_resolved": "https://registry.npmjs.org/browserify-aes/-/browserify-aes-1.2.0.tgz", "_spec": "1.2.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": "", "browser": "browser.js", "bugs": {"url": "https://github.com/crypto-browserify/browserify-aes/issues"}, "dependencies": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "description": "aes, for browserify", "devDependencies": {"standard": "^9.0.0", "tap-spec": "^4.1.1", "tape": "^4.6.3"}, "directories": {"test": "test"}, "homepage": "https://github.com/crypto-browserify/browserify-aes", "keywords": ["aes", "crypto", "browserify"], "license": "MIT", "main": "index.js", "name": "browserify-aes", "repository": {"type": "git", "url": "git://github.com/crypto-browserify/browserify-aes.git"}, "scripts": {"standard": "standard", "test": "npm run standard && npm run unit", "unit": "node test/index.js | tspec"}, "version": "1.2.0"}