{"_args": [["minimist@0.0.8", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "minimist@0.0.8", "_id": "minimist@0.0.8", "_inBundle": false, "_integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "_location": "/minimist", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "minimist@0.0.8", "name": "minimist", "escapedName": "minimist", "rawSpec": "0.0.8", "saveSpec": null, "fetchSpec": "0.0.8"}, "_requiredBy": ["/mkdirp"], "_resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "_spec": "0.0.8", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/minimist/issues"}, "description": "parse argument options", "devDependencies": {"tap": "~0.4.0", "tape": "~1.0.4"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "license": "MIT", "main": "index.js", "name": "minimist", "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "version": "0.0.8"}