{"_from": "html-webpack-plugin@^2.30.1", "_id": "html-webpack-plugin@2.30.1", "_inBundle": false, "_integrity": "sha512-TKQYvHTJYUwPgXzwUF3EwPPkyQyvzfz+6s8Fw2eamxl0cRin1tDnYppcDYWz8UIoYMX4CgatplRq18odzmpAWw==", "_location": "/html-webpack-plugin", "_phantomChildren": {"json5": "0.5.1", "object-assign": "4.1.1"}, "_requested": {"type": "range", "registry": true, "raw": "html-webpack-plugin@^2.30.1", "name": "html-webpack-plugin", "escapedName": "html-webpack-plugin", "rawSpec": "^2.30.1", "saveSpec": null, "fetchSpec": "^2.30.1"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-2.30.1.tgz", "_shasum": "7f9c421b7ea91ec460f56527d78df484ee7537d5", "_spec": "html-webpack-plugin@^2.30.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/ampedandwired"}, "bugs": {"url": "https://github.com/jantimon/html-webpack-plugin/issues"}, "bundleDependencies": false, "dependencies": {"bluebird": "^3.4.7", "html-minifier": "^3.2.3", "loader-utils": "^0.2.16", "lodash": "^4.17.3", "pretty-error": "^2.0.2", "toposort": "^1.0.0"}, "deprecated": "out of support", "description": "Simplifies creation of HTML files to serve your webpack bundles", "devDependencies": {"appcache-webpack-plugin": "^1.3.0", "css-loader": "^0.26.1", "dir-compare": "1.3.0", "es6-promise": "^4.0.5", "extract-text-webpack-plugin": "^1.0.1", "file-loader": "^0.9.0", "html-loader": "^0.4.4", "jade": "^1.11.0", "jade-loader": "^0.8.0", "jasmine": "^2.5.2", "rimraf": "^2.5.4", "semistandard": "8.0.0", "style-loader": "^0.13.1", "underscore-template-loader": "^0.7.3", "url-loader": "^0.5.7", "webpack": "^1.14.0", "webpack-recompilation-simulator": "^1.3.0"}, "files": ["index.js", "default_index.ejs", "lib/"], "homepage": "https://github.com/jantimon/html-webpack-plugin", "keywords": ["webpack", "plugin", "html", "html-webpack-plugin"], "license": "MIT", "main": "index.js", "name": "html-webpack-plugin", "peerDependencies": {"webpack": "1 || ^2 || ^2.1.0-beta || ^2.2.0-rc || ^3"}, "repository": {"type": "git", "url": "git+https://github.com/jantimon/html-webpack-plugin.git"}, "scripts": {"build-examples": "node examples/build-examples.js", "prepublish": "npm run test", "pretest": "semistandard", "test": "jasmine"}, "semistandard": {"ignore": ["examples/*/dist/**/*.*"]}, "version": "2.30.1"}