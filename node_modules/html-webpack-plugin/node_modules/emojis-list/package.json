{"_from": "emojis-list@^2.0.0", "_id": "emojis-list@2.1.0", "_inBundle": false, "_integrity": "sha512-knHEZMgs8BB+MInokmNTg/OyPlAddghe1YBgNwJBc5zsJi/uyIcXoSDsL/W9ymOsBoBGdPIHXYJ9+qKFwRwDng==", "_location": "/html-webpack-plugin/emojis-list", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "emojis-list@^2.0.0", "name": "emojis-list", "escapedName": "emojis-list", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/html-webpack-plugin/loader-utils"], "_resolved": "https://registry.npmjs.org/emojis-list/-/emojis-list-2.1.0.tgz", "_shasum": "4daa4d9db00f9819880c79fa457ae5b09a1fd389", "_spec": "emojis-list@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/html-webpack-plugin/node_modules/loader-utils", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Kikobeats"}, "bugs": {"url": "https://github.com/Kikobeats/emojis-list/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Complete list of standard emojis.", "devDependencies": {"acho": "latest", "browserify": "latest", "cheerio": "latest", "got": ">=5 <6", "gulp": "latest", "gulp-header": "latest", "gulp-uglify": "latest", "gulp-util": "latest", "standard": "latest", "vinyl-buffer": "latest", "vinyl-source-stream": "latest"}, "engines": {"node": ">= 0.10"}, "files": ["index.js"], "homepage": "https://github.com/Kikobeats/emojis-list", "keywords": ["archive", "complete", "emoji", "list", "standard"], "license": "MIT", "main": "./index.js", "name": "emojis-list", "repository": {"type": "git", "url": "git+https://github.com/kikobeats/emojis-list.git"}, "scripts": {"pretest": "standard update.js", "test": "echo 'YOLO'", "update": "node update"}, "version": "2.1.0"}