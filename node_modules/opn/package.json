{"_from": "opn@^5.1.0", "_id": "opn@5.5.0", "_inBundle": false, "_integrity": "sha512-PqHpggC9bLV0VeWcdKhkpxY+3JTzetLSqTCWL/z/tFIbI6G8JCjondXklT1JinczLz2Xib62sSp0T/gKT4KksA==", "_location": "/opn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "opn@^5.1.0", "name": "opn", "escapedName": "opn", "rawSpec": "^5.1.0", "saveSpec": null, "fetchSpec": "^5.1.0"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://registry.npmjs.org/opn/-/opn-5.5.0.tgz", "_shasum": "fc7164fab56d235904c51c3b27da6758ca3b9bfc", "_spec": "opn@^5.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/webpack-dev-server", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/opn/issues"}, "bundleDependencies": false, "dependencies": {"is-wsl": "^1.1.0"}, "deprecated": false, "description": "A better node-open. Opens stuff like websites, files, executables. Cross-platform.", "devDependencies": {"ava": "^0.25.0", "xo": "^0.20.0"}, "engines": {"node": ">=4"}, "files": ["index.js", "xdg-open"], "homepage": "https://github.com/sindresorhus/opn#readme", "keywords": ["app", "open", "opn", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "license": "MIT", "name": "opn", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/opn.git"}, "scripts": {"test": "xo"}, "version": "5.5.0"}