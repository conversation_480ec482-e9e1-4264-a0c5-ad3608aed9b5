{"_from": "parchment@^1.1.4", "_id": "parchment@1.1.4", "_inBundle": false, "_integrity": "sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg==", "_location": "/parchment", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "parchment@^1.1.4", "name": "parchment", "escapedName": "parchment", "rawSpec": "^1.1.4", "saveSpec": null, "fetchSpec": "^1.1.4"}, "_requiredBy": ["/quill"], "_resolved": "https://registry.npmjs.org/parchment/-/parchment-1.1.4.tgz", "_shasum": "aeded7ab938fe921d4c34bc339ce1168bc2ffde5", "_spec": "parchment@^1.1.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/quill", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/quilljs/parchment/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A document model for rich text editors", "devDependencies": {"babel-core": "^6.26.0", "istanbul": "~0.4.5", "jasmine-core": "^2.9.1", "karma": "^2.0.0", "karma-babel-preprocessor": "^7.0.0", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.1", "karma-jasmine": "^1.1.1", "karma-sauce-launcher": "^1.2.0", "karma-webpack": "^2.0.9", "ts-loader": "^3.4.0", "typescript": "^2.7.1", "webpack": "^3.10.0"}, "files": ["tsconfig.json", "dist", "src"], "homepage": "http://quilljs.com/docs/parchment", "license": "BSD-3-<PERSON><PERSON>", "main": "dist/parchment.js", "name": "parchment", "repository": {"type": "git", "url": "git+https://github.com/quilljs/parchment.git"}, "scripts": {"build": "webpack --config webpack.conf.js", "prepublish": "npm run build", "test": "karma start", "test:server": "karma start --no-single-run", "test:travis": "karma start --browsers saucelabs-chrome --reporters dots,saucelabs"}, "types": "dist/src/parchment.d.ts", "version": "1.1.4"}