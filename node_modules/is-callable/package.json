{"_from": "is-callable@^1.2.7", "_id": "is-callable@1.2.7", "_inBundle": false, "_integrity": "sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==", "_location": "/is-callable", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-callable@^1.2.7", "name": "is-callable", "escapedName": "is-callable", "rawSpec": "^1.2.7", "saveSpec": null, "fetchSpec": "^1.2.7"}, "_requiredBy": ["/es-abstract", "/es-to-primitive", "/for-each", "/function.prototype.name"], "_resolved": "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz", "_shasum": "3bc2a85ea742d9e36205dcacdd72ca1fdc51b055", "_spec": "is-callable@^1.2.7", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "v1.2.5"}, "bugs": {"url": "https://github.com/inspect-js/is-callable/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "deprecated": false, "description": "Is this JS value callable? Works with Functions and GeneratorFunctions, despite ES6 @@toStringTag.", "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.0", "auto-changelog": "^2.4.0", "available-typed-arrays": "^1.0.5", "eclint": "^2.8.1", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "has-tostringtag": "^1.0.0", "make-arrow-function": "^1.2.0", "make-async-function": "^1.0.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object-inspect": "^1.12.2", "rimraf": "^2.7.1", "safe-publish-latest": "^2.0.0", "tape": "^5.6.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-callable#readme", "keywords": ["Function", "function", "callable", "generator", "generator function", "arrow", "arrow function", "ES6", "toStringTag", "@@toStringTag"], "license": "MIT", "main": "index.js", "name": "is-callable", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-callable.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only --", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.2.7"}