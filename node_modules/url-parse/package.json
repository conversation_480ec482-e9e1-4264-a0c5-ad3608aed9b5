{"_args": [["url-parse@1.5.10", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "url-parse@1.5.10", "_id": "url-parse@1.5.10", "_inBundle": false, "_integrity": "sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==", "_location": "/url-parse", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "url-parse@1.5.10", "name": "url-parse", "escapedName": "url-parse", "rawSpec": "1.5.10", "saveSpec": null, "fetchSpec": "1.5.10"}, "_requiredBy": ["/original", "/sockjs-client"], "_resolved": "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz", "_spec": "1.5.10", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/unshiftio/url-parse/issues"}, "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}, "description": "Small footprint URL parser that works seamlessly across Node.js and browser environments", "devDependencies": {"assume": "^2.2.0", "browserify": "^17.0.0", "c8": "^7.3.1", "mocha": "^9.0.3", "pre-commit": "^1.2.2", "sauce-browsers": "^2.0.0", "sauce-test": "^1.3.3", "uglify-js": "^3.5.7"}, "files": ["index.js", "dist"], "homepage": "https://github.com/unshiftio/url-parse#readme", "keywords": ["URL", "parser", "uri", "url", "parse", "query", "string", "querystring", "stringify"], "license": "MIT", "main": "index.js", "name": "url-parse", "repository": {"type": "git", "url": "git+https://github.com/unshiftio/url-parse.git"}, "scripts": {"browserify": "rm -rf dist && mkdir -p dist && browserify index.js -s URLParse -o dist/url-parse.js", "minify": "uglifyjs dist/url-parse.js --source-map -cm -o dist/url-parse.min.js", "prepublishOnly": "npm run browserify && npm run minify", "test": "c8 --reporter=lcov --reporter=text mocha test/test.js", "test-browser": "node test/browser.js", "watch": "mocha --watch test/test.js"}, "version": "1.5.10"}