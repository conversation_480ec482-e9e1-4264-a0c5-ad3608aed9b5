{"_from": "mime-types@~2.1.19", "_id": "mime-types@2.1.35", "_inBundle": false, "_integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "_location": "/mime-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mime-types@~2.1.19", "name": "mime-types", "escapedName": "mime-types", "rawSpec": "~2.1.19", "saveSpec": null, "fetchSpec": "~2.1.19"}, "_requiredBy": ["/accepts", "/form-data", "/request", "/serve-index", "/type-is"], "_resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "_shasum": "381a871b62a734450660ae3deee44813f70d959a", "_spec": "mime-types@~2.1.19", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/request", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://searchbeam.jit.su"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"mime-db": "1.52.0"}, "deprecated": false, "description": "The ultimate javascript content-type utility.", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "homepage": "https://github.com/jshttp/mime-types#readme", "keywords": ["mime", "types"], "license": "MIT", "name": "mime-types", "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-types.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "2.1.35"}