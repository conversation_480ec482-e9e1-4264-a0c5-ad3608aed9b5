{"_from": "events@^3.0.0", "_id": "events@3.3.0", "_inBundle": false, "_integrity": "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==", "_location": "/events", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "events@^3.0.0", "name": "events", "escapedName": "events", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "_shasum": "31a95ad0a924e2d2c419a813aeb2c4e878ea7400", "_spec": "events@^3.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/node-libs-browser", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeditoolkit.com"}, "bugs": {"url": "http://github.com/Gozala/events/issues/"}, "bundleDependencies": false, "deprecated": false, "description": "<PERSON>de's event emitter for all engines.", "devDependencies": {"airtap": "^1.0.0", "functions-have-names": "^1.2.1", "has": "^1.0.3", "has-symbols": "^1.0.1", "isarray": "^2.0.5", "tape": "^5.0.0"}, "engines": {"node": ">=0.8.x"}, "homepage": "https://github.com/Gozala/events#readme", "keywords": ["events", "eventEmitter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listeners"], "license": "MIT", "main": "./events.js", "name": "events", "repository": {"type": "git", "url": "git://github.com/Gozala/events.git", "web": "https://github.com/Gozala/events"}, "scripts": {"test": "node tests/index.js", "test:browsers": "airtap -- tests/index.js"}, "version": "3.3.0"}