{"_args": [["js-tokens@3.0.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "js-tokens@3.0.2", "_id": "js-tokens@3.0.2", "_inBundle": false, "_integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls=", "_location": "/js-tokens", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "js-tokens@3.0.2", "name": "js-tokens", "escapedName": "js-tokens", "rawSpec": "3.0.2", "saveSpec": null, "fetchSpec": "3.0.2"}, "_requiredBy": ["/babel-code-frame", "/loose-envify"], "_resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz", "_spec": "3.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "description": "A regex that tokenizes JavaScript.", "devDependencies": {"coffee-script": "~1.12.6", "esprima": "^4.0.0", "everything.js": "^1.0.3", "mocha": "^3.4.2"}, "files": ["index.js"], "homepage": "https://github.com/lydell/js-tokens#readme", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "license": "MIT", "name": "js-tokens", "repository": {"type": "git", "url": "git+https://github.com/lydell/js-tokens.git"}, "scripts": {"build": "node generate-index.js", "dev": "npm run build && npm test", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js", "test": "mocha --ui tdd"}, "version": "3.0.2"}