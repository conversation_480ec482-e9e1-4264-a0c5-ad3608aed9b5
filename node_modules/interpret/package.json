{"_from": "interpret@^1.0.0", "_id": "interpret@1.4.0", "_inBundle": false, "_integrity": "sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==", "_location": "/interpret", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "interpret@^1.0.0", "name": "interpret", "escapedName": "interpret", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/shelljs", "/webpack"], "_resolved": "https://registry.npmjs.org/interpret/-/interpret-1.4.0.tgz", "_shasum": "665ab8bc4da27a774a40584e812e3e0fa45b1a1e", "_spec": "interpret@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/shelljs", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "bugs": {"url": "https://github.com/gulpjs/interpret/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://goingslowly.com/"}], "dependencies": {}, "deprecated": false, "description": "A dictionary of file extensions and associated module loaders.", "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3", "parse-node-version": "^1.0.0", "rechoir": "^0.6.2", "shelljs": "0.7.5", "trash-cli": "^3.0.0"}, "engines": {"node": ">= 0.10"}, "files": ["LICENSE", "index.js", "mjs-stub.js"], "homepage": "https://github.com/gulpjs/interpret#readme", "keywords": ["cirru-script", "cjsx", "co", "coco", "coffee", "coffee-script", "coffee.md", "coffeescript", "csv", "<PERSON><PERSON><PERSON>", "es", "es6", "iced", "iced.md", "iced-coffee-script", "ini", "js", "json", "json5", "jsx", "react", "litcoffee", "liticed", "ls", "livescript", "toml", "ts", "typescript", "wisp", "xml", "yaml", "yml"], "license": "MIT", "main": "index.js", "name": "interpret", "repository": {"type": "git", "url": "git+https://github.com/gulpjs/interpret.git"}, "scripts": {"cover": "istanbul cover _mocha --report lcovonly", "coveralls": "npm run cover && istanbul-coveralls", "lint": "eslint .", "pretest": "rm -rf tmp/ && npm run lint", "test": "mocha --async-only"}, "version": "1.4.0"}