{"_from": "eventemitter3@^2.0.3", "_id": "eventemitter3@2.0.3", "_inBundle": false, "_integrity": "sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg==", "_location": "/eventemitter3", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "eventemitter3@^2.0.3", "name": "eventemitter3", "escapedName": "eventemitter3", "rawSpec": "^2.0.3", "saveSpec": null, "fetchSpec": "^2.0.3"}, "_requiredBy": ["/quill"], "_resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.3.tgz", "_shasum": "b5e1079b59fb5e1ba2771c0a993be060a58c99ba", "_spec": "eventemitter3@^2.0.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/quill", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "bundleDependencies": false, "deprecated": false, "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "devDependencies": {"assume": "~1.4.1", "browserify": "~14.1.0", "mocha": "~3.2.0", "nyc": "~10.2.0", "pre-commit": "~1.2.0", "uglify-js": "~2.8.20", "zuul": "~3.11.1"}, "homepage": "https://github.com/primus/eventemitter3#readme", "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "license": "MIT", "main": "index.js", "name": "eventemitter3", "pre-commit": "sync, test", "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "scripts": {"benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "build": "mkdir -p umd && browserify index.js -s EventEmitter3 | uglifyjs -m -o umd/eventemitter3.min.js", "prepublish": "npm run build", "sync": "node versions.js", "test": "nyc --reporter=html --reporter=text mocha", "test-browser": "zuul -- test.js"}, "typings": "index.d.ts", "version": "2.0.3"}