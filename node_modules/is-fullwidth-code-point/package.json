{"_from": "is-fullwidth-code-point@^3.0.0", "_id": "is-fullwidth-code-point@3.0.0", "_inBundle": false, "_integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "_location": "/is-fullwidth-code-point", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-fullwidth-code-point@^3.0.0", "name": "is-fullwidth-code-point", "escapedName": "is-fullwidth-code-point", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/string-width"], "_resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "_shasum": "f116f8064fe90b3f7844a38997c0b75051269f1d", "_spec": "is-fullwidth-code-point@^3.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/string-width", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if the character represented by a given Unicode code point is fullwidth", "devDependencies": {"ava": "^1.3.1", "tsd-check": "^0.5.0", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "string", "codepoint", "code", "point", "is", "detect", "check"], "license": "MIT", "name": "is-fullwidth-code-point", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "scripts": {"test": "xo && ava && tsd-check"}, "version": "3.0.0"}