{"_args": [["lcid@1.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "lcid@1.0.0", "_id": "lcid@1.0.0", "_inBundle": false, "_integrity": "sha512-YiGkH6EnGrDGqLMITnGjXtGmNtjoXw9SVUzcaos8RBi7Ps0VBylkq+vOcY9QE5poLasPCR849ucFUkl0UzUyOw==", "_location": "/lcid", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "lcid@1.0.0", "name": "lcid", "escapedName": "lcid", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/os-locale", "/webpack-dev-server/os-locale"], "_resolved": "https://registry.npmjs.org/lcid/-/lcid-1.0.0.tgz", "_spec": "1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/lcid/issues"}, "dependencies": {"invert-kv": "^1.0.0"}, "description": "Mapping between standard locale identifiers and Windows locale identifiers (LCID)", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "lcid.json"], "homepage": "https://github.com/sindresorhus/lcid#readme", "keywords": ["lcid", "locale", "string", "str", "id", "identifier", "windows", "language", "lang", "map", "mapping", "convert", "json", "bcp47", "ietf", "tag"], "license": "MIT", "name": "lcid", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/lcid.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.0"}