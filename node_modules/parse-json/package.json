{"_from": "parse-json@^5.0.0", "_id": "parse-json@5.2.0", "_inBundle": false, "_integrity": "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==", "_location": "/parse-json", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "parse-json@^5.0.0", "name": "parse-json", "escapedName": "parse-json", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["/read-pkg"], "_resolved": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "_shasum": "c76fc66dee54231c962b22bcc8a72cf2f99753cd", "_spec": "parse-json@^5.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/read-pkg", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/parse-json/issues"}, "bundleDependencies": false, "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "deprecated": false, "description": "Parse JSO<PERSON> with more helpful errors", "devDependencies": {"ava": "^1.4.1", "nyc": "^14.1.1", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "vendor"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/parse-json#readme", "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "license": "MIT", "name": "parse-json", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/parse-json.git"}, "scripts": {"test": "xo && nyc ava"}, "version": "5.2.0"}