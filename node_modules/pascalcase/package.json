{"_from": "pascalcase@^0.1.1", "_id": "pascalcase@0.1.1", "_inBundle": false, "_integrity": "sha512-XHXfu/yOQRy9vYOtUDVMN60OEJjW013GoObG1o+xwQTpB9eYJX/BjXMsdW13ZDPruFhYYn0AG22w0xgQMwl3Nw==", "_location": "/pascalcase", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pascalcase@^0.1.1", "name": "pascalcase", "escapedName": "pascalcase", "rawSpec": "^0.1.1", "saveSpec": null, "fetchSpec": "^0.1.1"}, "_requiredBy": ["/base"], "_resolved": "https://registry.npmjs.org/pascalcase/-/pascalcase-0.1.1.tgz", "_shasum": "b363e55e8006ca6fe21784d2db22bd15d7917f14", "_spec": "pascalcase@^0.1.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/base", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/pascalcase/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Convert a string to pascal-case.", "devDependencies": {"mocha": "*", "should": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/pascalcase", "keywords": ["camelcase", "case", "casing", "pascal", "pascal-case", "pascalcase", "string"], "license": "MIT", "main": "index.js", "name": "pascalcase", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/pascalcase.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["pad-left", "pad-right", "word-wrap", "repeat-string", "justified"]}}, "version": "0.1.1"}