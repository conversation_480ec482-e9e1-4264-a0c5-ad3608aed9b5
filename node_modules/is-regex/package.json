{"_args": [["is-regex@1.2.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "is-regex@1.2.1", "_id": "is-regex@1.2.1", "_inBundle": false, "_integrity": "sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==", "_location": "/is-regex", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "is-regex@1.2.1", "name": "is-regex", "escapedName": "is-regex", "rawSpec": "1.2.1", "saveSpec": null, "fetchSpec": "1.2.1"}, "_requiredBy": ["/deep-equal", "/es-abstract", "/safe-regex-test", "/which-builtin-type"], "_resolved": "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz", "_spec": "1.2.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-regex/issues"}, "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "description": "Is this value a JS regex? Works cross-realm/iframe, and despite ES6 @@toStringTag", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/core-js": "^2.5.8", "@types/for-each": "^0.3.3", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "core-js": "^3.39.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "^5.8.0-dev.20241129"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-regex", "keywords": ["regex", "regexp", "is", "regular expression", "regular", "expression"], "license": "MIT", "main": "index.js", "name": "is-regex", "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-regex.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:harmony", "test:corejs": "nyc tape test-corejs.js", "test:harmony": "nyc node --harmony --es-staging test", "tests-only": "nyc node test", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.2.1"}