{"_from": "neo-async@^2.5.0", "_id": "neo-async@2.6.2", "_inBundle": false, "_integrity": "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==", "_location": "/neo-async", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "neo-async@^2.5.0", "name": "neo-async", "escapedName": "neo-async", "rawSpec": "^2.5.0", "saveSpec": null, "fetchSpec": "^2.5.0"}, "_requiredBy": ["/sass-loader", "/watchpack"], "_resolved": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz", "_shasum": "b4aafb93e3aeb2d8174ca53cf163ab7d7308305f", "_spec": "neo-async@^2.5.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/sass-loader", "browser": "async.min.js", "bugs": {"url": "https://github.com/suguru03/neo-async/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Neo-Async is a drop-in replacement for Async, it almost fully covers its functionality and runs faster ", "devDependencies": {"aigle": "^1.14.0", "async": "^2.6.0", "benchmark": "^2.1.1", "bluebird": "^3.5.1", "codecov.io": "^0.1.6", "fs-extra": "^4.0.2", "func-comparator": "^0.7.2", "gulp": "^4.0.2", "gulp-bump": "^2.8.0", "gulp-exit": "0.0.2", "gulp-git": "^2.4.2", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "husky": "^1.2.0", "istanbul": "^0.4.3", "jsdoc": "^3.5.5", "jshint": "^2.9.5", "lint-staged": "^8.1.0", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.5.3", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.3", "prettier": "^1.15.2", "require-dir": "^0.3.0"}, "homepage": "https://github.com/suguru03/neo-async", "keywords": ["async", "util"], "license": "MIT", "lint-staged": {"*.{js,ts}": ["prettier --write", "git add"]}, "main": "async.js", "name": "neo-async", "prettier": {"printWidth": 100, "singleQuote": true}, "repository": {"type": "git", "url": "git+ssh://**************/suguru03/neo-async.git"}, "version": "2.6.2"}