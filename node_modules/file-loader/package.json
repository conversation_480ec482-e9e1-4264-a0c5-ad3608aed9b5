{"_from": "file-loader@^1.1.4", "_id": "file-loader@1.1.11", "_inBundle": false, "_integrity": "sha512-TGR4HU7HUsGg6GCOPJnFk06RhWgEWFLAGWiT6rcD+GRC2keU3s9RGJ+b3Z6/U73jwwNb2gKLJ7YCrp+jvU4ALg==", "_location": "/file-loader", "_phantomChildren": {"ajv": "6.12.6", "ajv-keywords": "3.5.2"}, "_requested": {"type": "range", "registry": true, "raw": "file-loader@^1.1.4", "name": "file-loader", "escapedName": "file-loader", "rawSpec": "^1.1.4", "saveSpec": null, "fetchSpec": "^1.1.4"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/file-loader/-/file-loader-1.1.11.tgz", "_shasum": "6fe886449b0f2a936e43cabaac0cdbfb369506f8", "_spec": "file-loader@^1.1.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack/file-loader/issues"}, "bundleDependencies": false, "dependencies": {"loader-utils": "^1.0.2", "schema-utils": "^0.4.5"}, "deprecated": false, "description": "file loader module for webpack", "devDependencies": {"babel-cli": "^6.0.0", "babel-jest": "^21.0.0", "babel-plugin-transform-object-rest-spread": "^6.0.0", "babel-polyfill": "^6.0.0", "babel-preset-env": "^1.0.0", "cross-env": "^5.0.0", "del": "^3.0.0", "del-cli": "^1.0.0", "eslint": "^4.0.0", "eslint-config-webpack": "^1.0.0", "eslint-plugin-import": "^2.0.0", "jest": "^21.0.0", "lint-staged": "^5.0.0", "memory-fs": "^0.4.0", "nsp": "^2.0.0", "pre-commit": "^1.0.0", "standard-version": "^4.0.0", "webpack": "^3.0.0", "webpack-defaults": "^1.6.0"}, "engines": {"node": ">= 4.3 < 5.0.0 || >= 5.10"}, "files": ["dist"], "homepage": "https://webpack.js.org/loaders/file-loader", "license": "MIT", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "main": "dist/cjs.js", "name": "file-loader", "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "pre-commit": "lint-staged", "repository": {"type": "git", "url": "git+https://github.com/webpack/file-loader.git"}, "scripts": {"appveyor:test": "npm run test", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "prebuild": "npm run clean", "prepare": "npm run build", "release": "standard-version", "security": "nsp check", "start": "npm run build -- -w", "test": "jest", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "test:watch": "jest --watch", "travis:coverage": "npm run test:coverage -- --runInBand", "travis:lint": "npm run lint && npm run security", "travis:test": "npm run test -- --runInBand", "webpack-defaults": "webpack-defaults"}, "version": "1.1.11"}