{"_from": "schema-utils@^0.4.5", "_id": "schema-utils@0.4.7", "_inBundle": false, "_integrity": "sha512-v/iwU6wvwGK8HbU9yi3/nhGzP0yGSuhQMzL6ySiec1FSrZZDkhm4noOSWzrNFo/jEc+SJY6jRTwuwbSXJPDUnQ==", "_location": "/file-loader/schema-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "schema-utils@^0.4.5", "name": "schema-utils", "escapedName": "schema-utils", "rawSpec": "^0.4.5", "saveSpec": null, "fetchSpec": "^0.4.5"}, "_requiredBy": ["/file-loader"], "_resolved": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.7.tgz", "_shasum": "ba74f597d2be2ea880131746ee17d0a093c68187", "_spec": "schema-utils@^0.4.5", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/file-loader", "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "bundleDependencies": false, "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "deprecated": false, "description": "webpack Validation Utils", "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-conventional": "^7.0.0", "@webpack-contrib/eslint-config-webpack": "^2.0.0", "del-cli": "^1.0.0", "eslint": "^5.0.0", "eslint-plugin-import": "^2.0.0", "eslint-plugin-prettier": "^2.0.0", "jest": "^21.0.0", "prettier": "^1.0.0", "standard-version": "^4.0.0"}, "engines": {"node": ">= 4"}, "files": ["src"], "homepage": "https://github.com/webpack-contrib/schema-utils", "license": "MIT", "main": "src/index.js", "name": "schema-utils", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "scripts": {"clean": "del-cli coverage", "commits": "commitlint --from $(git rev-list --tags --max-count=1)", "lint": "eslint --cache src test", "release": "npm run commits && standard-version", "test": "jest --env node --verbose --coverage"}, "version": "0.4.7"}