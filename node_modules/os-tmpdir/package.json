{"_args": [["os-tmpdir@1.0.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "os-tmpdir@1.0.2", "_id": "os-tmpdir@1.0.2", "_inBundle": false, "_integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "_location": "/os-tmpdir", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "os-tmpdir@1.0.2", "name": "os-tmpdir", "escapedName": "os-tmpdir", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/home-or-tmp"], "_resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "_spec": "1.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/os-tmpdir/issues"}, "description": "Node.js os.tmpdir() ponyfill", "devDependencies": {"ava": "*", "xo": "^0.16.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/os-tmpdir#readme", "keywords": ["built-in", "core", "ponyfill", "polyfill", "shim", "os", "tmpdir", "tempdir", "tmp", "temp", "dir", "directory", "env", "environment"], "license": "MIT", "name": "os-tmpdir", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/os-tmpdir.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.2"}