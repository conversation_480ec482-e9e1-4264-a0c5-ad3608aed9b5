{"_from": "has-values@^1.0.0", "_id": "has-values@1.0.0", "_inBundle": false, "_integrity": "sha512-ODYZC64uqzmtfGMEAX/FvZiRyWLpAC3vYnNunURUnkGVTS+mI0smVsWaPydRBsE3g+ok7h960jChO8mFcWlHaQ==", "_location": "/has-values", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "has-values@^1.0.0", "name": "has-values", "escapedName": "has-values", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/has-value"], "_resolved": "https://registry.npmjs.org/has-values/-/has-values-1.0.0.tgz", "_shasum": "95b0b63fec2146619a6fe57fe75628d5a39efe4f", "_spec": "has-values@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/has-value", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/has-values/issues"}, "bundleDependencies": false, "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "deprecated": false, "description": "Returns true if any values exist, false if empty. Works for booleans, functions, numbers, strings, nulls, objects and arrays. ", "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.4.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/has-values", "keywords": ["array", "boolean", "empty", "find", "function", "has", "hasOwn", "javascript", "js", "key", "keys", "node.js", "null", "number", "object", "properties", "property", "string", "type", "util", "utilities", "utility", "value", "values"], "license": "MIT", "main": "index.js", "name": "has-values", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/has-values.git"}, "scripts": {"test": "mocha"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["has-value", "kind-of", "is-number", "is-plain-object", "isobject"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}, "version": "1.0.0"}