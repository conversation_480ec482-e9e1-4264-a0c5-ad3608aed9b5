{"_args": [["convert-source-map@1.5.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "convert-source-map@1.5.1", "_id": "convert-source-map@1.5.1", "_inBundle": false, "_integrity": "sha1-uCeAl7m8IpNl3lxiz1/K7YtVmeU=", "_location": "/convert-source-map", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "convert-source-map@1.5.1", "name": "convert-source-map", "escapedName": "convert-source-map", "rawSpec": "1.5.1", "saveSpec": null, "fetchSpec": "1.5.1"}, "_requiredBy": ["/babel-core"], "_resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.5.1.tgz", "_spec": "1.5.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com"}, "bugs": {"url": "https://github.com/thlorenz/convert-source-map/issues"}, "dependencies": {}, "description": "Converts a source-map from/to  different formats and allows adding/changing properties.", "devDependencies": {"inline-source-map": "~0.6.2", "tap": "~9.0.0"}, "engine": {"node": ">=0.6"}, "files": ["index.js"], "homepage": "https://github.com/thlorenz/convert-source-map", "keywords": ["convert", "sourcemap", "source", "map", "browser", "debug"], "license": "MIT", "main": "index.js", "name": "convert-source-map", "repository": {"type": "git", "url": "git://github.com/thlorenz/convert-source-map.git"}, "scripts": {"test": "tap test/*.js --color"}, "version": "1.5.1"}