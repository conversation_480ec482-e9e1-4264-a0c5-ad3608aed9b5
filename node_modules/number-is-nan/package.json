{"_args": [["number-is-nan@1.0.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "number-is-nan@1.0.1", "_id": "number-is-nan@1.0.1", "_inBundle": false, "_integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=", "_location": "/number-is-nan", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "number-is-nan@1.0.1", "name": "number-is-nan", "escapedName": "number-is-nan", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/is-finite"], "_resolved": "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz", "_spec": "1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/number-is-nan/issues"}, "description": "ES2015 Number.isNaN() ponyfill", "devDependencies": {"ava": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/number-is-nan#readme", "keywords": ["es2015", "ecmascript", "ponyfill", "polyfill", "shim", "number", "is", "nan", "not"], "license": "MIT", "name": "number-is-nan", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/number-is-nan.git"}, "scripts": {"test": "ava"}, "version": "1.0.1"}