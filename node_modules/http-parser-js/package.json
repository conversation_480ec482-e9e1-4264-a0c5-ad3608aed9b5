{"_from": "http-parser-js@>=0.5.1", "_id": "http-parser-js@0.5.10", "_inBundle": false, "_integrity": "sha512-Pysuw9XpUq5dVc/2SMHpuTY01RFl8fttgcyunjL7eEMhGM3cI4eOmiCycJDVCo/7O7ClfQD3SaI6ftDzqOXYMA==", "_location": "/http-parser-js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "http-parser-js@>=0.5.1", "name": "http-parser-js", "escapedName": "http-parser-js", "rawSpec": ">=0.5.1", "saveSpec": null, "fetchSpec": ">=0.5.1"}, "_requiredBy": ["/websocket-driver"], "_resolved": "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.10.tgz", "_shasum": "b3277bd6d7ed5588e20ea73bf724fcbe44609075", "_spec": "http-parser-js@>=0.5.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/websocket-driver", "author": {"name": "<PERSON>", "url": "https://github.com/creationix"}, "bugs": {"url": "https://github.com/creationix/http-parser-js/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/Jimbly"}, {"name": "<PERSON>", "url": "https://github.com/lrowe"}, {"name": "<PERSON>", "url": "https://github.com/jscissr"}, {"name": "<PERSON>", "url": "https://github.com/paulrutter"}], "deprecated": false, "description": "A pure JS HTTP parser for node.", "files": ["http-parser.js", "http-parser.d.ts"], "homepage": "https://github.com/creationix/http-parser-js#readme", "keywords": ["http"], "license": "MIT", "main": "http-parser.js", "name": "http-parser-js", "repository": {"type": "git", "url": "git://github.com/creationix/http-parser-js.git"}, "scripts": {"test": "python tests/test.py && node tests/iojs/test-http-parser-durability.js", "testv12": "python tests/test.py --node-args=\"--http-parser=legacy\" && node --http-parser=legacy tests/iojs/test-http-parser-durability.js"}, "types": "http-parser.d.ts", "version": "0.5.10"}