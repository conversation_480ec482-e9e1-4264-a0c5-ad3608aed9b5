{"_from": "loglevel@^1.4.1", "_id": "loglevel@1.9.2", "_inBundle": false, "_integrity": "sha512-HgMmCqIJSAKqo68l0rS2AanEWfkxaZ5wNiEFb5ggm08lDs9Xl2KxBlX3PTcaD2chBM1gXAYf491/M2Rv8Jwayg==", "_location": "/loglevel", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "loglevel@^1.4.1", "name": "loglevel", "escapedName": "loglevel", "rawSpec": "^1.4.1", "saveSpec": null, "fetchSpec": "^1.4.1"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://registry.npmjs.org/loglevel/-/loglevel-1.9.2.tgz", "_shasum": "c2e028d6c757720107df4e64508530db6621ba08", "_spec": "loglevel@^1.4.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/webpack-dev-server", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tim-perry.co.uk"}, "bugs": {"url": "https://github.com/pimterry/loglevel/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Minimal lightweight logging for JavaScript, adding reliable log level methods to any available console.log methods", "devDependencies": {"@types/core-js": "2.5.0", "@types/node": "^12.0.4", "grunt": "~1.5.3", "grunt-cli": "^1.4.3", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-concat": "~0.5.0", "grunt-contrib-connect": "^3.0.0", "grunt-contrib-jasmine": "^4.0.0", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-uglify": "^3.4.0", "grunt-contrib-watch": "^1.1.0", "grunt-open": "~0.2.3", "grunt-preprocess": "^5.1.0", "jasmine": "^2.4.1", "ts-node": "^10.9.2", "typescript": "^3.5.1"}, "engines": {"node": ">= 0.6.0"}, "funding": {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/loglevel"}, "homepage": "https://github.com/pimterry/loglevel", "keywords": ["log", "logger", "logging", "browser"], "license": "MIT", "main": "lib/loglevel.js", "name": "loglevel", "repository": {"type": "git", "url": "git://github.com/pimterry/loglevel.git"}, "scripts": {"dist": "grunt dist", "dist-build": "grunt dist-build", "lint": "grunt jshint", "test": "grunt test && npm run test-types", "test-browser": "grunt test-browser", "test-node": "grunt test-node", "test-types": "tsc --noEmit ./test/type-test.ts && ts-node ./test/type-test.ts", "watch": "grunt watch"}, "types": "./index.d.ts", "version": "1.9.2"}