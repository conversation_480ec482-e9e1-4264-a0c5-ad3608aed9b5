{"_from": "is-plain-obj@^1.1.0", "_id": "is-plain-obj@1.1.0", "_inBundle": false, "_integrity": "sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==", "_location": "/is-plain-obj", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-plain-obj@^1.1.0", "name": "is-plain-obj", "escapedName": "is-plain-obj", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/merge-options", "/minimist-options", "/sort-keys"], "_resolved": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "_shasum": "71a50c8429dfca773c92a390a4a03b39fcd51d3e", "_spec": "is-plain-obj@^1.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/minimist-options", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-plain-obj/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if a value is a plain object", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/is-plain-obj#readme", "keywords": ["obj", "object", "is", "check", "test", "type", "plain", "vanilla", "pure", "simple"], "license": "MIT", "name": "is-plain-obj", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-plain-obj.git"}, "scripts": {"test": "node test.js"}, "version": "1.1.0"}