{"_from": "map-obj@^4.0.0", "_id": "map-obj@4.3.0", "_inBundle": false, "_integrity": "sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==", "_location": "/map-obj", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "map-obj@^4.0.0", "name": "map-obj", "escapedName": "map-obj", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/camelcase-keys"], "_resolved": "https://registry.npmjs.org/map-obj/-/map-obj-4.3.0.tgz", "_shasum": "9304f906e93faae70880da102a9f1df0ea8bb05a", "_spec": "map-obj@^4.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/camelcase-keys", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/map-obj/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Map object keys and values into a new object", "devDependencies": {"ava": "^2.0.0", "tsd": "^0.14.0", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/map-obj#readme", "keywords": ["map", "object", "key", "keys", "value", "values", "iterate", "iterator", "rename", "modify", "deep", "recurse", "recursive"], "license": "MIT", "name": "map-obj", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/map-obj.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.3.0"}