{"_from": "min-indent@^1.0.0", "_id": "min-indent@1.0.1", "_inBundle": false, "_integrity": "sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==", "_location": "/min-indent", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "min-indent@^1.0.0", "name": "min-indent", "escapedName": "min-indent", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/strip-indent"], "_resolved": "https://registry.npmjs.org/min-indent/-/min-indent-1.0.1.tgz", "_shasum": "a63f681673b30571fbe8bc25686ae746eefa9869", "_spec": "min-indent@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/strip-indent", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "thejameskyle.com"}, "bugs": {"url": "https://github.com/thejameskyle/min-indent/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Get the shortest leading whitespace from lines in a string", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/thejameskyle/min-indent#readme", "keywords": ["indent", "indentation", "normalize", "whitespace", "space", "tab", "string", "str", "min", "minimum"], "license": "MIT", "main": "index.js", "name": "min-indent", "repository": {"type": "git", "url": "git+https://github.com/thejameskyle/min-indent.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.1"}