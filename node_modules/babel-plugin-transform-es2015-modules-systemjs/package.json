{"_args": [["babel-plugin-transform-es2015-modules-systemjs@6.24.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "babel-plugin-transform-es2015-modules-systemjs@6.24.1", "_id": "babel-plugin-transform-es2015-modules-systemjs@6.24.1", "_inBundle": false, "_integrity": "sha512-ONFIPsq8y4bls5PPsAWYXH/21Hqv64TBxdje0FvU3MhIV6QM2j5YS7KvAzg/nTIVLot2D2fmFQrFWCbgHlFEjg==", "_location": "/babel-plugin-transform-es2015-modules-systemjs", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-plugin-transform-es2015-modules-systemjs@6.24.1", "name": "babel-plugin-transform-es2015-modules-systemjs", "escapedName": "babel-plugin-transform-es2015-modules-systemjs", "rawSpec": "6.24.1", "saveSpec": null, "fetchSpec": "6.24.1"}, "_requiredBy": ["/babel-preset-env"], "_resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz", "_spec": "6.24.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "dependencies": {"babel-helper-hoist-variables": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}, "description": "This plugin transforms ES2015 modules to SystemJS", "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1", "babel-plugin-syntax-dynamic-import": "^6.18.0"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "babel-plugin-transform-es2015-modules-systemjs", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-es2015-modules-systemjs"}, "version": "6.24.1"}