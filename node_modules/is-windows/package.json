{"_from": "is-windows@^1.0.2", "_id": "is-windows@1.0.2", "_inBundle": false, "_integrity": "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==", "_location": "/is-windows", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-windows@^1.0.2", "name": "is-windows", "escapedName": "is-windows", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/nanomatch"], "_resolved": "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz", "_shasum": "d1850eb9791ecd18e6182ce12a30f396634bb19d", "_spec": "is-windows@^1.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/nanomatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-windows/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/SimenB"}, {"name": "刘祺", "url": "gucong.co.cc"}], "deprecated": false, "description": "Returns true if the platform is windows. UMD module, works with node.js, commonjs, browser, AMD, electron, etc.", "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-windows", "keywords": ["check", "cywin", "is", "is-windows", "nix", "operating system", "os", "platform", "process", "unix", "win", "win32", "windows"], "license": "MIT", "main": "index.js", "name": "is-windows", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-windows.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-absolute", "is-glob", "is-relative", "isobject", "window-size"]}, "lint": {"reflinks": true}, "reflinks": ["verb"]}, "version": "1.0.2"}