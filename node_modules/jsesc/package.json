{"_args": [["jsesc@1.3.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "jsesc@1.3.0", "_id": "jsesc@1.3.0", "_inBundle": false, "_integrity": "sha1-RsP+yMGJKxKwgz25vHYiF226s0s=", "_location": "/jsesc", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "jsesc@1.3.0", "name": "jsesc", "escapedName": "jsesc", "rawSpec": "1.3.0", "saveSpec": null, "fetchSpec": "1.3.0"}, "_requiredBy": ["/babel-generator"], "_resolved": "https://registry.npmjs.org/jsesc/-/jsesc-1.3.0.tgz", "_spec": "1.3.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bin": {"jsesc": "bin/jsesc"}, "bugs": {"url": "https://github.com/mathiasbynens/jsesc/issues"}, "description": "A JavaScript library for escaping JavaScript strings while generating the shortest possible valid output.", "devDependencies": {"coveralls": "^2.11.6", "grunt": "^0.4.5", "grunt-shell": "^1.1.2", "grunt-template": "^0.2.3", "istanbul": "^0.4.2", "qunit-extras": "^1.4.5", "qunitjs": "~1.11.0", "regenerate": "^1.2.1", "requirejs": "^2.1.22"}, "files": ["LICENSE-MIT.txt", "jsesc.js", "bin/", "man/"], "homepage": "https://mths.be/jsesc", "keywords": ["string", "escape", "javascript", "tool"], "license": "MIT", "main": "jsesc.js", "man": ["man/jsesc.1"], "name": "jsesc", "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/jsesc.git"}, "scripts": {"build": "grunt template", "test": "node tests/tests.js"}, "version": "1.3.0"}