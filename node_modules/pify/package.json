{"_from": "pify@^3.0.0", "_id": "pify@3.0.0", "_inBundle": false, "_integrity": "sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==", "_location": "/pify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pify@^3.0.0", "name": "pify", "escapedName": "pify", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/del", "/globby", "/gzip-size", "/make-dir", "/path-type"], "_resolved": "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz", "_shasum": "e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176", "_spec": "pify@^3.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/make-dir", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Promisify a callback-style function", "devDependencies": {"ava": "*", "pinkie-promise": "^2.0.0", "v8-natives": "^1.0.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/pify#readme", "keywords": ["promise", "promises", "promisify", "all", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "await", "es2015", "bluebird"], "license": "MIT", "name": "pify", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/pify.git"}, "scripts": {"optimization-test": "node --allow-natives-syntax optimization-test.js", "test": "xo && ava && npm run optimization-test"}, "version": "3.0.0"}