{"_from": "lodash.memoize@^4.1.2", "_id": "lodash.memoize@4.1.2", "_inBundle": false, "_integrity": "sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==", "_location": "/lodash.memoize", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lodash.memoize@^4.1.2", "name": "lodash.memoize", "escapedName": "lodash.memoize", "rawSpec": "^4.1.2", "saveSpec": null, "fetchSpec": "^4.1.2"}, "_requiredBy": ["/caniuse-api", "/cssnano-preset-default/caniuse-api"], "_resolved": "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "_shasum": "bcc6c49a42a2840ed997f323eada5ecd182e0bfe", "_spec": "lodash.memoize@^4.1.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/caniuse-api", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "deprecated": false, "description": "The lodash method `_.memoize` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "keywords": ["lodash-modularized", "memoize"], "license": "MIT", "name": "lodash.memoize", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "version": "4.1.2"}