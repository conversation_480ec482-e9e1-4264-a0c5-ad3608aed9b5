(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('element/locale/az', ['exports'], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports);
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports);
    global.ELEMENT.lang = global.ELEMENT.lang || {}; 
    global.ELEMENT.lang.az = mod.exports;
  }
})(this, function (exports) {
  'use strict';

  exports.__esModule = true;
  exports.default = {
    el: {
      colorpicker: {
        confirm: 'Təsdiqlə',
        clear: 'Təmizlə'
      },
      datepicker: {
        now: 'İndi',
        today: 'Bugün',
        cancel: 'İmtina',
        clear: 'Təmizlə',
        confirm: 'Təsdiqlə',
        selectDate: 'Taxir seç',
        selectTime: 'Saat seç',
        startDate: 'Başlanğıc Tarixi',
        startTime: 'Başlanğıc Saatı',
        endDate: 'Bitmə Tarixi',
        endTime: 'Bitmə Saatı',
        prevYear: 'Öncəki il',
        nextYear: 'Son<PERSON>ı il',
        prevMonth: 'Öncəki ay',
        nextMonth: 'Sonrakı ay',
        year: '',
        month1: 'Yanvar',
        month2: 'Fevral',
        month3: 'Mart',
        month4: 'Aprel',
        month5: 'May',
        month6: 'İyun',
        month7: 'İyul',
        month8: 'Avqust',
        month9: 'Sentyabr',
        month10: 'Oktyabr',
        month11: 'Noyabr',
        month12: 'Dekabr',
        // week: 'week',
        weeks: {
          sun: 'Baz',
          mon: 'B.e',
          tue: 'Ç.a',
          wed: 'Çər',
          thu: 'C.a',
          fri: 'Cüm',
          sat: 'Şən'
        },
        months: {
          jan: 'Yan',
          feb: 'Fev',
          mar: 'Mar',
          apr: 'Apr',
          may: 'May',
          jun: 'İyn',
          jul: 'İyl',
          aug: 'Avq',
          sep: 'Sen',
          oct: 'Okt',
          nov: 'Noy',
          dec: 'Dek'
        }
      },
      select: {
        loading: 'Yüklənir',
        noMatch: 'Nəticə tapılmadı',
        noData: 'Məlumat yoxdur',
        placeholder: 'Seç'
      },
      cascader: {
        noMatch: 'Nəticə tapılmadı',
        loading: 'Yüklənir',
        placeholder: 'Seç',
        noData: 'Məlumat yoxdur'
      },
      pagination: {
        goto: 'Get',
        pagesize: '/səhifə',
        total: 'Toplam {total}',
        pageClassifier: ''
      },
      messagebox: {
        title: 'Mesaj',
        confirm: 'Təsdiqlə',
        cancel: 'İmtina',
        error: 'Səhv'
      },
      upload: {
        deleteTip: 'Sürüşdürmədən sonra sil',
        delete: 'Sil',
        preview: 'Ön izlə',
        continue: 'Davam et'
      },
      table: {
        emptyText: 'Məlumat yoxdur',
        confirmFilter: 'Təsdiqlə',
        resetFilter: 'Sıfırla',
        clearFilter: 'Bütün',
        sumText: 'Cəmi'
      },
      tree: {
        emptyText: 'Məlumat yoxdur'
      },
      transfer: {
        noMatch: 'Nəticə tapılmadı',
        noData: 'Məlumat yoxdur',
        titles: ['List 1', 'List 2'],
        filterPlaceholder: 'Kəliməliri daxil et',
        noCheckedFormat: '{total} ədəd',
        hasCheckedFormat: '{checked}/{total} seçildi'
      },
      image: {
        error: 'SƏHV' // to be translated
      },
      pageHeader: {
        title: 'Geri' // to be translated
      },
      popconfirm: {
        confirmButtonText: 'Bəli', // to be translated
        cancelButtonText: 'Xeyr' // to be translated
      },
      empty: {
        description: 'Məlumat yoxdur'
      }
    }
  };
});