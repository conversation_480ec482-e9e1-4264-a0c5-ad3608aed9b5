{"_args": [["has-property-descriptors@1.0.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "has-property-descriptors@1.0.2", "_id": "has-property-descriptors@1.0.2", "_inBundle": false, "_integrity": "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==", "_location": "/has-property-descriptors", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "has-property-descriptors@1.0.2", "name": "has-property-descriptors", "escapedName": "has-property-descriptors", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/define-properties", "/es-abstract", "/set-function-length", "/set-function-name", "/string.prototype.trim"], "_resolved": "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "_spec": "1.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/has-property-descriptors/issues"}, "dependencies": {"es-define-property": "^1.0.0"}, "description": "Does the environment have full property descriptor support? Handles IE 8's broken defineProperty/gOPD.", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/has-property-descriptors#readme", "keywords": ["property", "descriptors", "has", "environment", "env", "defineProperty", "getOwnPropertyDescriptor"], "license": "MIT", "main": "index.js", "name": "has-property-descriptors", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-property-descriptors.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js"}, "version": "1.0.2"}