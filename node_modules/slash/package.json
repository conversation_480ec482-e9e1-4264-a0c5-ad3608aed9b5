{"_args": [["slash@1.0.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "slash@1.0.0", "_id": "slash@1.0.0", "_inBundle": false, "_integrity": "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=", "_location": "/slash", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "slash@1.0.0", "name": "slash", "escapedName": "slash", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/babel-core"], "_resolved": "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz", "_spec": "1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "description": "Convert Windows backslash paths to slash paths", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/slash#readme", "keywords": ["path", "seperator", "sep", "slash", "backslash", "windows", "win"], "license": "MIT", "name": "slash", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/slash.git"}, "scripts": {"test": "mocha"}, "version": "1.0.0"}