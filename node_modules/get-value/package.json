{"_from": "get-value@^2.0.6", "_id": "get-value@2.0.6", "_inBundle": false, "_integrity": "sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA==", "_location": "/get-value", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "get-value@^2.0.6", "name": "get-value", "escapedName": "get-value", "rawSpec": "^2.0.6", "saveSpec": null, "fetchSpec": "^2.0.6"}, "_requiredBy": ["/cache-base", "/has-value", "/union-value", "/unset-value/has-value"], "_resolved": "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz", "_shasum": "dc15ca1c672387ca76bd37ac0a395ba2042a2c28", "_spec": "get-value@^2.0.6", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/cache-base", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/get-value/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Use property paths (`a.b.c`) to get a nested value from an object.", "devDependencies": {"ansi-bold": "^0.1.1", "arr-reduce": "^1.0.1", "benchmarked": "^0.1.4", "dot-prop": "^2.2.0", "getobject": "^0.1.0", "gulp": "^3.9.0", "gulp-eslint": "^1.1.1", "gulp-format-md": "^0.1.5", "gulp-istanbul": "^0.10.2", "gulp-mocha": "^2.1.3", "isobject": "^2.0.0", "matched": "^0.3.2", "minimist": "^1.2.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/get-value", "keywords": ["get", "key", "nested", "object", "path", "paths", "prop", "properties", "property", "props", "segment", "value", "values"], "license": "MIT", "main": "index.js", "name": "get-value", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/get-value.git"}, "scripts": {"test": "mocha"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["has-any", "has-any-deep", "has-value", "set-value", "unset-value"]}, "reflinks": ["verb", "verb-readme-generator"], "lint": {"reflinks": true}}, "version": "2.0.6"}