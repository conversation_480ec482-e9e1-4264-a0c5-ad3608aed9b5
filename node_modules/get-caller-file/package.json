{"_from": "get-caller-file@^2.0.5", "_id": "get-caller-file@2.0.5", "_inBundle": false, "_integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "_location": "/get-caller-file", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "get-caller-file@^2.0.5", "name": "get-caller-file", "escapedName": "get-caller-file", "rawSpec": "^2.0.5", "saveSpec": null, "fetchSpec": "^2.0.5"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "_shasum": "4f94412a82db32f36e3b0b9741f8a97feb031f7e", "_spec": "get-caller-file@^2.0.5", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/yargs", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "bundleDependencies": false, "deprecated": false, "description": "[![Build Status](https://travis-ci.org/stefanpenner/get-caller-file.svg?branch=master)](https://travis-ci.org/stefanpenner/get-caller-file) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.appveyor.com/project/embercli/get-caller-file/branch/master)", "devDependencies": {"@types/chai": "^4.1.7", "@types/ensure-posix-path": "^1.0.0", "@types/mocha": "^5.2.6", "@types/node": "^11.10.5", "chai": "^4.1.2", "ensure-posix-path": "^1.0.1", "mocha": "^5.2.0", "typescript": "^3.3.3333"}, "directories": {"test": "tests"}, "engines": {"node": "6.* || 8.* || >= 10.*"}, "files": ["index.js", "index.js.map", "index.d.ts"], "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "license": "ISC", "main": "index.js", "name": "get-caller-file", "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "scripts": {"prepare": "tsc", "test": "mocha test", "test:debug": "mocha test"}, "version": "2.0.5"}