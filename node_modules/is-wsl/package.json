{"_from": "is-wsl@^1.1.0", "_id": "is-wsl@1.1.0", "_inBundle": false, "_integrity": "sha512-gfygJYZ2gLTDlmbWMI0CE2MwnFzSN/2SZfkMlItC4K/JBlsWVDB0bO6XhqcY13YXE7iMcAJnzTCJjPiTeJJ0Mw==", "_location": "/is-wsl", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-wsl@^1.1.0", "name": "is-wsl", "escapedName": "is-wsl", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/node-notifier", "/opn"], "_resolved": "https://registry.npmjs.org/is-wsl/-/is-wsl-1.1.0.tgz", "_shasum": "1f16e4aa22b04d1336b66188a66af3c600c3a66d", "_spec": "is-wsl@^1.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/node-notifier", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-wsl/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if the process is running inside Windows Subsystem for Linux (Bash on Windows)", "devDependencies": {"ava": "*", "clear-require": "^2.0.0", "proxyquire": "^1.7.11", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/is-wsl#readme", "keywords": ["check", "wsl", "windows", "subsystem", "linux", "detect", "bash", "process", "console", "terminal", "is"], "license": "MIT", "name": "is-wsl", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-wsl.git"}, "scripts": {"test": "xo && ava"}, "version": "1.1.0"}