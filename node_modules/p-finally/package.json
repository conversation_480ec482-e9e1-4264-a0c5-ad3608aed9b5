{"_from": "p-finally@^1.0.0", "_id": "p-finally@1.0.0", "_inBundle": false, "_integrity": "sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==", "_location": "/p-finally", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "p-finally@^1.0.0", "name": "p-finally", "escapedName": "p-finally", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/execa"], "_resolved": "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz", "_shasum": "3fbcfb15b899a44123b34b6dcc18b724336a2cae", "_spec": "p-finally@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/execa", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-finally/issues"}, "bundleDependencies": false, "deprecated": false, "description": "`Promise#finally()` ponyfill - Invoked when the promise is settled regardless of outcome", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/p-finally#readme", "keywords": ["promise", "finally", "handler", "function", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "license": "MIT", "name": "p-finally", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-finally.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.0", "xo": {"esnext": true}}