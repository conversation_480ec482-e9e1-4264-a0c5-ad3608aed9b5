{"_from": "lodash.camelcase@^4.3.0", "_id": "lodash.camelcase@4.3.0", "_inBundle": false, "_integrity": "sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==", "_location": "/lodash.camelcase", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lodash.camelcase@^4.3.0", "name": "lodash.camelcase", "escapedName": "lodash.camelcase", "rawSpec": "^4.3.0", "saveSpec": null, "fetchSpec": "^4.3.0"}, "_requiredBy": ["/css-loader"], "_resolved": "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz", "_shasum": "b28aa6288a2b9fc651035c7711f65ab6190331a6", "_spec": "lodash.camelcase@^4.3.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/css-loader", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "deprecated": false, "description": "The lodash method `_.camelCase` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "keywords": ["lodash-modularized", "camelcase"], "license": "MIT", "name": "lodash.camelcase", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "version": "4.3.0"}