{"_from": "normalize-wheel@^1.0.1", "_id": "normalize-wheel@1.0.1", "_inBundle": false, "_integrity": "sha512-1OnlAPZ3zgrk8B91HyRj+eVv+kS5u+Z0SCsak6Xil/kmgEia50ga7zfkumayonZrImffAxPU/5WcyGhzetHNPA==", "_location": "/normalize-wheel", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "normalize-wheel@^1.0.1", "name": "normalize-wheel", "escapedName": "normalize-wheel", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/element-ui"], "_resolved": "https://registry.npmjs.org/normalize-wheel/-/normalize-wheel-1.0.1.tgz", "_shasum": "aec886affdb045070d856447df62ecf86146ec45", "_spec": "normalize-wheel@^1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/element-ui", "author": {"name": "<PERSON><PERSON>"}, "bundleDependencies": false, "deprecated": false, "description": "Mouse wheel normalization across multiple multiple browsers.", "keywords": ["mouse wheel", "normalization", "browser"], "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "name": "normalize-wheel", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.1"}