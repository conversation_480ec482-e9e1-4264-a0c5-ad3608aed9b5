{"_args": [["shellwords@0.1.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "shellwords@0.1.1", "_id": "shellwords@0.1.1", "_inBundle": false, "_integrity": "sha512-vFwSUfQvqybiICwZY5+DAWIPLKsWO31Q91JSKl3UYv+K5c2QRPzn0qzec6QPu1Qc9eHYItiP3NdJqNVqetYAww==", "_location": "/shellwords", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "shellwords@0.1.1", "name": "shellwords", "escapedName": "shellwords", "rawSpec": "0.1.1", "saveSpec": null, "fetchSpec": "0.1.1"}, "_requiredBy": ["/node-notifier"], "_resolved": "https://registry.npmjs.org/shellwords/-/shellwords-0.1.1.tgz", "_spec": "0.1.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jimmycuadra.com/"}, "bugs": {"url": "https://github.com/jimmycuadra/shellwords/issues"}, "dependencies": {}, "description": "Manipulate strings according to the word parsing rules of the UNIX Bourne shell.", "devDependencies": {"jasmine-node": "~1.0.26", "nodewatch": "~0.1.0"}, "files": ["lib"], "homepage": "https://github.com/jimmycuadra/shellwords", "license": "MIT", "main": "./lib/shellwords", "name": "shellwords", "repository": {"type": "git", "url": "git://github.com/jimmycuadra/shellwords.git"}, "scripts": {"test": "cake spec"}, "version": "0.1.1"}