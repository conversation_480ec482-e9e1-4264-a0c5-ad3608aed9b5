{"_from": "shebang-regex@^1.0.0", "_id": "shebang-regex@1.0.0", "_inBundle": false, "_integrity": "sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==", "_location": "/execa/shebang-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "shebang-regex@^1.0.0", "name": "shebang-regex", "escapedName": "shebang-regex", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/execa/shebang-command"], "_resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "_shasum": "da42f49740c0b42db2ca9728571cb190c98efea3", "_spec": "shebang-regex@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/execa/node_modules/shebang-command", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/shebang-regex/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Regular expression for matching a shebang", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/shebang-regex#readme", "keywords": ["re", "regex", "regexp", "shebang", "match", "test"], "license": "MIT", "name": "shebang-regex", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/shebang-regex.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.0"}