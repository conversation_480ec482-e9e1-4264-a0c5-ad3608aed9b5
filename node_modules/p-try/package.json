{"_from": "p-try@^2.0.0", "_id": "p-try@2.2.0", "_inBundle": false, "_integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "_location": "/p-try", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "p-try@^2.0.0", "name": "p-try", "escapedName": "p-try", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/p-limit"], "_resolved": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "_shasum": "cb2868540e313d61de58fafbe35ce9004d5540e6", "_spec": "p-try@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/p-limit", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-try/issues"}, "bundleDependencies": false, "deprecated": false, "description": "`Start a promise chain", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/p-try#readme", "keywords": ["promise", "try", "resolve", "function", "catch", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "license": "MIT", "name": "p-try", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-try.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.2.0"}