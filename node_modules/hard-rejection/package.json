{"_from": "hard-rejection@^2.1.0", "_id": "hard-rejection@2.1.0", "_inBundle": false, "_integrity": "sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==", "_location": "/hard-rejection", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "hard-rejection@^2.1.0", "name": "hard-rejection", "escapedName": "hard-rejection", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/meow"], "_resolved": "https://registry.npmjs.org/hard-rejection/-/hard-rejection-2.1.0.tgz", "_shasum": "1c6eda5c1685c63942766d79bb40ae773cecd883", "_spec": "hard-rejection@^2.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/meow", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/hard-rejection/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Make unhandled promise rejections fail hard right away instead of the default silent fail", "devDependencies": {"ava": "^1.4.1", "execa": "^1.0.0", "tsd": "^0.7.1", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts", "register.js"], "homepage": "https://github.com/sindresorhus/hard-rejection#readme", "keywords": ["promise", "promises", "unhandled", "uncaught", "rejection", "hard", "fail", "catch", "throw", "handler", "exit", "debug", "debugging", "verbose", "immediate", "immediately"], "license": "MIT", "name": "hard-rejection", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/hard-rejection.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.1.0"}