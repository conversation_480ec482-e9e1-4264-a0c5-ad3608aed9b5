{"_from": "num2fraction@^1.2.2", "_id": "num2fraction@1.2.2", "_inBundle": false, "_integrity": "sha512-Y1wZESM7VUThYY+4W+X4ySH2maqcA+p7UR+w8VWNWVAd6lwuXXWz/w/Cz43J/dI2I+PS6wD5N+bJUF+gjWvIqg==", "_location": "/num2fraction", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "num2fraction@^1.2.2", "name": "num2fraction", "escapedName": "num2fraction", "rawSpec": "^1.2.2", "saveSpec": null, "fetchSpec": "^1.2.2"}, "_requiredBy": ["/autoprefixer", "/cssnano/autoprefixer"], "_resolved": "https://registry.npmjs.org/num2fraction/-/num2fraction-1.2.2.tgz", "_shasum": "6f682b6a027a4e9ddfa4564cd2589d1d4e669ede", "_spec": "num2fraction@^1.2.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/autoprefixer", "author": {"name": "yisi", "email": "y<PERSON><PERSON>@gmail.com", "url": "http://iyunlu.com/view"}, "bugs": {"url": "https://github.com/yisibl/num2fraction/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Convert number to fraction", "devDependencies": {"tape": "^3.0.0"}, "homepage": "https://github.com/yisibl/num2fraction#readme", "keywords": ["fraction", "number", "math", "maths", "arithmetic", "gcd", "rational"], "license": "MIT", "main": "index.js", "name": "num2fraction", "repository": {"type": "git", "url": "git+ssh://**************/yisibl/num2fraction.git"}, "scripts": {"test": "tape test/*.js"}, "version": "1.2.2"}