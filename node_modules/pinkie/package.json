{"_from": "pinkie@^2.0.0", "_id": "pinkie@2.0.4", "_inBundle": false, "_integrity": "sha512-MnUuEycAemtSaeFSjXKW/aroV7akBbY+Sv+RkyqFjgAe73F+MR0TBWKBRDkmfWq/HiFmdavfZ1G7h4SPZXaCSg==", "_location": "/pinkie", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pinkie@^2.0.0", "name": "pinkie", "escapedName": "pinkie", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/pinkie-promise"], "_resolved": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz", "_shasum": "72556b80cfa0d48a974e80e77248e80ed4f7f870", "_spec": "pinkie@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/pinkie-promise", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "bugs": {"url": "https://github.com/floatdrop/pinkie/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Itty bitty little widdle twinkie pinkie ES2015 Promise implementation", "devDependencies": {"core-assert": "^0.1.1", "coveralls": "^2.11.4", "mocha": "*", "nyc": "^3.2.2", "promises-aplus-tests": "*", "xo": "^0.10.1"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/floatdrop/pinkie#readme", "keywords": ["promise", "promises", "es2015", "es6"], "license": "MIT", "name": "pinkie", "repository": {"type": "git", "url": "git+https://github.com/floatdrop/pinkie.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "test": "xo && nyc mocha"}, "version": "2.0.4"}