{"_args": [["call-bound@1.0.4", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "call-bound@1.0.4", "_id": "call-bound@1.0.4", "_inBundle": false, "_integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "_location": "/call-bound", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "call-bound@1.0.4", "name": "call-bound", "escapedName": "call-bound", "rawSpec": "1.0.4", "saveSpec": null, "fetchSpec": "1.0.4"}, "_requiredBy": ["/array-buffer-byte-length", "/array-includes", "/array.prototype.reduce", "/data-view-buffer", "/data-view-byte-length", "/data-view-byte-offset", "/es-abstract", "/function.prototype.name", "/get-symbol-description", "/is-arguments", "/is-array-buffer", "/is-async-function", "/is-boolean-object", "/is-data-view", "/is-date-object", "/is-finalizationregistry", "/is-generator-function", "/is-number-object", "/is-regex", "/is-shared-array-buffer", "/is-string", "/is-symbol", "/is-weakref", "/is-weakset", "/object.assign", "/object.values", "/safe-array-concat", "/safe-regex-test", "/side-channel-map", "/side-channel-weakmap", "/string.prototype.trim", "/string.prototype.trimend", "/typed-array-buffer", "/unbox-primitive", "/which-builtin-type", "/which-typed-array"], "_resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "_spec": "1.0.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/call-bound/issues"}, "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "description": "Robust call-bound JavaScript intrinsics, using `call-bind` and `get-intrinsic`.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.4", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.3.0", "@types/call-bind": "^1.0.5", "@types/get-intrinsic": "^1.2.3", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "gopd": "^1.2.0", "has-strict-mode": "^1.1.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/call-bound#readme", "keywords": ["javascript", "ecmascript", "es", "js", "callbind", "callbound", "call", "bind", "bound", "call-bind", "call-bound", "function", "es-abstract"], "license": "MIT", "main": "index.js", "name": "call-bound", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/call-bound.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=auto", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js"}, "version": "1.0.4"}