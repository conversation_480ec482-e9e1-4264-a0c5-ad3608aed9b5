{"_args": [["sass-loader@7.3.1", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "sass-loader@7.3.1", "_id": "sass-loader@7.3.1", "_inBundle": false, "_integrity": "sha512-tuU7+zm0pTCynKYHpdqaPpe+MMTQ76I9TPZ7i4/5dZsigE350shQWe5EZNl5dBidM49TPET75tNqRbcsUZWeNA==", "_location": "/sass-loader", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "sass-loader@7.3.1", "name": "sass-loader", "escapedName": "sass-loader", "rawSpec": "7.3.1", "saveSpec": null, "fetchSpec": "7.3.1"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/sass-loader/-/sass-loader-7.3.1.tgz", "_spec": "7.3.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/webpack-contrib/sass-loader/issues"}, "dependencies": {"clone-deep": "^4.0.1", "loader-utils": "^1.0.1", "neo-async": "^2.5.0", "pify": "^4.0.1", "semver": "^6.3.0"}, "description": "Sass loader for webpack", "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "bootstrap": "^4.3.1", "bootstrap-sass": "^3.3.5", "chokidar": "^2.1.6", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "css-loader": "^3.2.0", "del": "^4.1.1", "del-cli": "^1.1.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.14.0", "file-loader": "^4.2.0", "husky": "^3.0.3", "jest": "^24.9.0", "jest-junit": "^7.0.0", "jquery": "^3.4.1", "lint-staged": "^9.2.1", "memory-fs": "^0.4.1", "node-sass": "^4.5.0", "npm-run-all": "^4.1.5", "nyc": "^14.1.1", "popper.js": "^1.15.0", "prettier": "^1.15.2", "raw-loader": "^3.1.0", "sass": "^1.3.0", "standard-version": "^7.0.0", "style-loader": "^1.0.0", "webpack": "^4.5.0", "webpack-cli": "^3.1.0", "webpack-dev-server": "^3.1.4"}, "engines": {"node": ">= 6.9.0"}, "files": ["dist"], "homepage": "https://github.com/webpack-contrib/sass-loader", "keywords": ["sass", "libsass", "webpack", "loader"], "license": "MIT", "main": "dist/cjs.js", "name": "sass-loader", "peerDependencies": {"webpack": "^3.0.0 || ^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/sass-loader.git"}, "scripts": {"build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "defaults": "webpack-defaults", "lint": "npm-run-all -l -p \"lint:**\"", "lint:js": "eslint --cache src test", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "prebuild": "npm run clean", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "security": "npm audit", "start": "npm run build -- -w", "test": "cross-env NODE_ENV=test npm run test:coverage", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "test:manual": "npm run build && webpack-dev-server test/manual/src/index.js --open --config test/manual/webpack.config.js", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch"}, "version": "7.3.1"}