{"_from": "isexe@^2.0.0", "_id": "isexe@2.0.0", "_inBundle": false, "_integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "_location": "/isexe", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "isexe@^2.0.0", "name": "isexe", "escapedName": "isexe", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/execa/which", "/node-notifier/which", "/which"], "_resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "_shasum": "e8fbf374dc556ff8947a10dcb0572d633f2cfa10", "_spec": "isexe@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/which", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/isexe/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Minimal module to check if a file is executable.", "devDependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.5.0", "tap": "^10.3.0"}, "directories": {"test": "test"}, "homepage": "https://github.com/isaacs/isexe#readme", "keywords": [], "license": "ISC", "main": "index.js", "name": "isexe", "repository": {"type": "git", "url": "git+https://github.com/isaacs/isexe.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js --100"}, "version": "2.0.0"}