{"_args": [["snapdragon@0.8.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "snapdragon@0.8.2", "_id": "snapdragon@0.8.2", "_inBundle": false, "_integrity": "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==", "_location": "/snapdragon", "_phantomChildren": {"is-descriptor": "0.1.7"}, "_requested": {"type": "version", "registry": true, "raw": "snapdragon@0.8.2", "name": "snapdragon", "escapedName": "snapdragon", "rawSpec": "0.8.2", "saveSpec": null, "fetchSpec": "0.8.2"}, "_requiredBy": ["/braces", "/expand-brackets", "/extglob", "/http-proxy-middleware/micromatch", "/micromatch", "/nanomatch", "/watchpack-chokidar2/micromatch", "/webpack-dev-server/micromatch"], "_resolved": "https://registry.npmjs.org/snapdragon/-/snapdragon-0.8.2.tgz", "_spec": "0.8.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/snapdragon/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://edwardbetts.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "dependencies": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "description": "Fast, pluggable and easy-to-use parser-renderer factory.", "devDependencies": {"gulp": "^3.9.1", "gulp-eslint": "^3.0.1", "gulp-format-md": "^0.1.10", "gulp-istanbul": "^1.1.1", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.0", "mocha": "^3.0.2"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "lib"], "homepage": "https://github.com/jonschlinkert/snapdragon", "keywords": ["lexer", "snapdragon"], "license": "MIT", "main": "index.js", "name": "snapdragon", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/snapdragon.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"description": "These libraries use snapdragon:", "list": ["braces", "expand-brackets", "extglob", "micromatch"]}, "reflinks": ["css", "pug", "verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "version": "0.8.2"}