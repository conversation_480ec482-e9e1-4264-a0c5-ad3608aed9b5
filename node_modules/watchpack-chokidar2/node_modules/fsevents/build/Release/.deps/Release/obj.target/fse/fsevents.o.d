cmd_Release/obj.target/fse/fsevents.o := c++ '-DNODE_GYP_MODULE_NAME=fse' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node -I/Users/<USER>/Library/Caches/node-gyp/14.21.3/src -I/Users/<USER>/Library/Caches/node-gyp/14.21.3/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/14.21.3/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/14.21.3/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/14.21.3/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/14.21.3/deps/v8/include -I../../../../nan  -O3 -gdwarf-2 -mmacosx-version-min=10.13 -arch x86_64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++1y -stdlib=libc++ -fno-rtti -fno-exceptions -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/fse/fsevents.o.d.raw   -c -o Release/obj.target/fse/fsevents.o ../fsevents.cc
Release/obj.target/fse/fsevents.o: ../fsevents.cc ../../../../nan/nan.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/node_version.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv/errno.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv/version.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv/unix.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv/threadpool.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv/darwin.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/node.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/v8.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/cppgc/common.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/v8config.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/v8-internal.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/v8-version.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/v8-platform.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/node_buffer.h \
  /Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/node_object_wrap.h \
  ../../../../nan/nan_callbacks.h ../../../../nan/nan_callbacks_12_inl.h \
  ../../../../nan/nan_maybe_43_inl.h ../../../../nan/nan_converters.h \
  ../../../../nan/nan_converters_43_inl.h ../../../../nan/nan_new.h \
  ../../../../nan/nan_implementation_12_inl.h \
  ../../../../nan/nan_persistent_12_inl.h ../../../../nan/nan_weak.h \
  ../../../../nan/nan_object_wrap.h ../../../../nan/nan_private.h \
  ../../../../nan/nan_typedarray_contents.h ../../../../nan/nan_json.h \
  ../../../../nan/nan_scriptorigin.h ../src/storage.cc ../src/async.cc \
  ../src/thread.cc ../src/constants.cc ../src/methods.cc
../fsevents.cc:
../../../../nan/nan.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/node_version.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv/errno.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv/version.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv/unix.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv/threadpool.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/uv/darwin.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/node.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/v8.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/cppgc/common.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/v8config.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/v8-internal.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/v8-version.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/v8-platform.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/node_buffer.h:
/Users/<USER>/Library/Caches/node-gyp/14.21.3/include/node/node_object_wrap.h:
../../../../nan/nan_callbacks.h:
../../../../nan/nan_callbacks_12_inl.h:
../../../../nan/nan_maybe_43_inl.h:
../../../../nan/nan_converters.h:
../../../../nan/nan_converters_43_inl.h:
../../../../nan/nan_new.h:
../../../../nan/nan_implementation_12_inl.h:
../../../../nan/nan_persistent_12_inl.h:
../../../../nan/nan_weak.h:
../../../../nan/nan_object_wrap.h:
../../../../nan/nan_private.h:
../../../../nan/nan_typedarray_contents.h:
../../../../nan/nan_json.h:
../../../../nan/nan_scriptorigin.h:
../src/storage.cc:
../src/async.cc:
../src/thread.cc:
../src/constants.cc:
../src/methods.cc:
