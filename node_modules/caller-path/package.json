{"_from": "caller-path@^2.0.0", "_id": "caller-path@2.0.0", "_inBundle": false, "_integrity": "sha512-MCL3sf6nCSXOwCTzvPKhN18TU7AHTvdtam8DAogxcrJ8Rjfbbg7Lgng64H9Iy+vUV6VGFClN/TyxBkAebLRR4A==", "_location": "/caller-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "caller-path@^2.0.0", "name": "caller-path", "escapedName": "caller-path", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/import-fresh"], "_resolved": "https://registry.npmjs.org/caller-path/-/caller-path-2.0.0.tgz", "_shasum": "468f83044e369ab2010fac5f06ceee15bb2cb1f4", "_spec": "caller-path@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/import-fresh", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/caller-path/issues"}, "bundleDependencies": false, "dependencies": {"caller-callsite": "^2.0.0"}, "deprecated": false, "description": "Get the path of the caller function", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/caller-path#readme", "keywords": ["caller", "calling", "module", "path", "parent", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "license": "MIT", "name": "caller-path", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/caller-path.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.0", "xo": {"esnext": true}}