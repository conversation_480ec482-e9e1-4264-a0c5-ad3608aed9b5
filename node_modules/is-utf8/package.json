{"_from": "is-utf8@^0.2.0", "_id": "is-utf8@0.2.1", "_inBundle": false, "_integrity": "sha512-rMYPYvCzsXywIsldgLaSoPlw5PfoB/ssr7hY4pLfcodrA5M/eArza1a9VmTiNIBNMjOGr1Ow9mTyU2o69U6U9Q==", "_location": "/is-utf8", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-utf8@^0.2.0", "name": "is-utf8", "escapedName": "is-utf8", "rawSpec": "^0.2.0", "saveSpec": null, "fetchSpec": "^0.2.0"}, "_requiredBy": ["/internal-ip/strip-bom", "/webpack-dev-server/strip-bom"], "_resolved": "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz", "_shasum": "4b0da1442104d1b336340e80797e865cf39f7d72", "_spec": "is-utf8@^0.2.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/strip-bom", "author": {"name": "wayfind"}, "bugs": {"url": "https://github.com/wayfind/is-utf8/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Detect if a buffer is utf8 encoded.", "files": ["is-utf8.js"], "homepage": "https://github.com/wayfind/is-utf8#readme", "keywords": ["utf8", "charset"], "license": "MIT", "main": "is-utf8.js", "name": "is-utf8", "repository": {"type": "git", "url": "git+https://github.com/wayfind/is-utf8.git"}, "scripts": {"test": "node test.js"}, "version": "0.2.1"}