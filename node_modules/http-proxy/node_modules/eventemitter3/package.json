{"_from": "eventemitter3@^4.0.0", "_id": "eventemitter3@4.0.7", "_inBundle": false, "_integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==", "_location": "/http-proxy/eventemitter3", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "eventemitter3@^4.0.0", "name": "eventemitter3", "escapedName": "eventemitter3", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/http-proxy"], "_resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "_shasum": "2de9b68f6528d5644ef5c59526a1b4a07306169f", "_spec": "eventemitter3@^4.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/http-proxy", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "bundleDependencies": false, "deprecated": false, "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "devDependencies": {"assume": "^2.2.0", "browserify": "^16.5.0", "mocha": "^8.0.1", "nyc": "^15.1.0", "pre-commit": "^1.2.0", "sauce-browsers": "^2.0.0", "sauce-test": "^1.3.3", "uglify-js": "^3.9.0"}, "files": ["index.js", "index.d.ts", "umd"], "homepage": "https://github.com/primus/eventemitter3#readme", "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "license": "MIT", "main": "index.js", "name": "eventemitter3", "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "scripts": {"benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "prepublishOnly": "npm run browserify && npm run minify", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "test-browser": "node test/browser.js"}, "typings": "index.d.ts", "version": "4.0.7"}