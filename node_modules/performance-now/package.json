{"_from": "performance-now@^2.1.0", "_id": "performance-now@2.1.0", "_inBundle": false, "_integrity": "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==", "_location": "/performance-now", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "performance-now@^2.1.0", "name": "performance-now", "escapedName": "performance-now", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "_shasum": "6309f4e0e5fa913ec1c69307ae364b4b377c9e7b", "_spec": "performance-now@^2.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/request", "author": {"name": "Braveg1rl", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/braveg1rl/performance-now/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Implements performance.now (based on process.hrtime).", "devDependencies": {"bluebird": "^3.4.7", "call-delayed": "^1.0.0", "chai": "^3.5.0", "chai-increasing": "^1.2.0", "coffee-script": "~1.12.2", "mocha": "~3.2.0", "pre-commit": "^1.2.2"}, "homepage": "https://github.com/braveg1rl/performance-now", "keywords": [], "license": "MIT", "main": "lib/performance-now.js", "name": "performance-now", "optionalDependencies": {}, "private": false, "repository": {"type": "git", "url": "git://github.com/braveg1rl/performance-now.git"}, "scripts": {"build": "mkdir -p lib && rm -rf lib/* && node_modules/.bin/coffee --compile -m --output lib/ src/", "prepublish": "npm test", "pretest": "npm run build", "test": "mocha", "watch": "coffee --watch --compile --output lib/ src/"}, "typings": "src/index.d.ts", "version": "2.1.0"}