{"_from": "opener@^1.4.3", "_id": "opener@1.5.2", "_inBundle": false, "_integrity": "sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==", "_location": "/opener", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "opener@^1.4.3", "name": "opener", "escapedName": "opener", "rawSpec": "^1.4.3", "saveSpec": null, "fetchSpec": "^1.4.3"}, "_requiredBy": ["/webpack-bundle-analyzer"], "_resolved": "https://registry.npmjs.org/opener/-/opener-1.5.2.tgz", "_shasum": "5d37e1f35077b9dcac4301372271afdeb2a13598", "_spec": "opener@^1.4.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/webpack-bundle-analyzer", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "bin": {"opener": "bin/opener-bin.js"}, "bugs": {"url": "https://github.com/domenic/opener/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Opens stuff, like webpages and files and executables, cross-platform", "devDependencies": {"eslint": "^7.7.0"}, "files": ["lib/", "bin/"], "homepage": "https://github.com/domenic/opener#readme", "license": "(WTFPL OR MIT)", "main": "lib/opener.js", "name": "opener", "repository": {"type": "git", "url": "git+https://github.com/domenic/opener.git"}, "scripts": {"lint": "eslint ."}, "version": "1.5.2"}