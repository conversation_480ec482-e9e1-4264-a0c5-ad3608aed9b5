{"_args": [["kind-of@6.0.3", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_from": "kind-of@6.0.3", "_id": "kind-of@6.0.3", "_inBundle": false, "_integrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==", "_location": "/kind-of", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "kind-of@6.0.3", "name": "kind-of", "escapedName": "kind-of", "rawSpec": "6.0.3", "saveSpec": null, "fetchSpec": "6.0.3"}, "_requiredBy": ["/clone-deep", "/http-proxy-middleware/micromatch", "/minimist-options", "/nanomatch", "/shallow-clone", "/watchpack-chokidar2/micromatch", "/webpack-dev-server/micromatch"], "_resolved": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz", "_spec": "6.0.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}], "description": "Get the native type of a value.", "devDependencies": {"benchmarked": "^2.0.0", "browserify": "^14.4.0", "gulp-format-md": "^1.0.0", "mocha": "^4.0.1", "write": "^1.0.3"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/kind-of", "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "license": "MIT", "main": "index.js", "name": "kind-of", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "scripts": {"prepublish": "browserify -o browser.js -e index.js -s index --bare", "test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["is-glob", "is-number", "is-primitive"]}, "reflinks": ["type-of", "typeof", "verb"]}, "version": "6.0.3"}