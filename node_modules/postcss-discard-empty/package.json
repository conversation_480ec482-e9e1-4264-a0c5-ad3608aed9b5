{"_args": [["postcss-discard-empty@2.1.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "postcss-discard-empty@2.1.0", "_id": "postcss-discard-empty@2.1.0", "_inBundle": false, "_integrity": "sha512-IBFoyrwk52dhF+5z/ZAbzq5Jy7Wq0aLUsOn69JNS+7YeuyHaNzJwBIYE0QlUH/p5d3L+OON72Fsexyb7OK/3og==", "_location": "/postcss-discard-empty", "_phantomChildren": {"escape-string-regexp": "1.0.5", "has-ansi": "2.0.0", "js-base64": "2.6.4", "strip-ansi": "3.0.1"}, "_requested": {"type": "version", "registry": true, "raw": "postcss-discard-empty@2.1.0", "name": "postcss-discard-empty", "escapedName": "postcss-discard-empty", "rawSpec": "2.1.0", "saveSpec": null, "fetchSpec": "2.1.0"}, "_requiredBy": ["/cssnano"], "_resolved": "https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-2.1.0.tgz", "_spec": "2.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "ava": {"require": "babel-core/register"}, "bugs": {"url": "https://github.com/ben-eb/postcss-discard-empty/issues"}, "dependencies": {"postcss": "^5.0.14"}, "description": "Discard empty rules and values with PostCSS.", "devDependencies": {"ava": "^0.14.0", "babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-plugin-add-module-exports": "^0.1.2", "babel-preset-es2015": "^6.3.13", "babel-preset-es2015-loose": "^7.0.0", "babel-preset-stage-0": "^6.3.13", "del-cli": "^0.2.0", "eslint": "^2.0.0", "eslint-config-cssnano": "^2.0.0"}, "eslintConfig": {"extends": "cssnano"}, "files": ["dist", "LICENSE-MIT"], "homepage": "https://github.com/ben-eb/postcss-discard-empty", "keywords": ["compress", "css", "empty", "minify", "optimisation", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-discard-empty", "repository": {"type": "git", "url": "git+https://github.com/ben-eb/postcss-discard-empty.git"}, "scripts": {"prepublish": "del-cli dist && babel src --out-dir dist --ignore /__tests__/", "pretest": "eslint src", "test": "ava src/__tests__"}, "version": "2.1.0"}