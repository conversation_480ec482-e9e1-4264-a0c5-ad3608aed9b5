{"_args": [["braces@2.3.2", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "braces@2.3.2", "_id": "braces@2.3.2", "_inBundle": false, "_integrity": "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==", "_location": "/braces", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "braces@2.3.2", "name": "braces", "escapedName": "braces", "rawSpec": "2.3.2", "saveSpec": null, "fetchSpec": "2.3.2"}, "_requiredBy": ["/http-proxy-middleware/micromatch", "/micromatch", "/watchpack-chokidar2/chokidar", "/watchpack-chokidar2/micromatch", "/webpack-dev-server/chokidar", "/webpack-dev-server/micromatch"], "_resolved": "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz", "_spec": "2.3.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "url": "https://github.com/eush77"}, {"name": "hemanth.hm", "url": "http://h3manth.com"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^2.0.0", "brace-expansion": "^1.1.8", "cross-spawn": "^5.1.0", "gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "mocha": "^3.2.0", "noncharacters": "^1.1.0", "text-table": "^0.2.0", "time-diff": "^0.3.1", "yargs-parser": "^8.0.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "lib"], "homepage": "https://github.com/micromatch/braces", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "license": "MIT", "main": "index.js", "name": "braces", "repository": {"type": "git", "url": "git+https://github.com/micromatch/braces.git"}, "scripts": {"benchmark": "node benchmark", "test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "lint": {"reflinks": true}, "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}}, "version": "2.3.2"}