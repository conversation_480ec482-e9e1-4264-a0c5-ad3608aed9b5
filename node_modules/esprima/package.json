{"_from": "esprima@^2.6.0", "_id": "esprima@2.7.3", "_inBundle": false, "_integrity": "sha512-OarPfz0lFCiW4/AV2Oy1Rp9qu0iusTKqykwTspGCZtPxmF81JR4MmIebvF1F9+UOKth2ZubLQ4XGGaU+hSn99A==", "_location": "/esprima", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "esprima@^2.6.0", "name": "esprima", "escapedName": "esprima", "rawSpec": "^2.6.0", "saveSpec": null, "fetchSpec": "^2.6.0"}, "_requiredBy": ["/js-yaml"], "_resolved": "https://registry.npmjs.org/esprima/-/esprima-2.7.3.tgz", "_shasum": "96e3b70d5779f6ad49cd032673d1c312767ba581", "_spec": "esprima@^2.6.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/js-yaml", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "bundleDependencies": false, "deprecated": false, "description": "ECMAScript parsing infrastructure for multipurpose analysis", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "eslint": "~1.7.2", "everything.js": "~1.0.3", "glob": "^5.0.15", "istanbul": "~0.4.0", "jscs": "~2.3.5", "json-diff": "~0.3.1", "karma": "^0.13.11", "karma-chrome-launcher": "^0.2.1", "karma-detect-browsers": "^2.0.2", "karma-firefox-launcher": "^0.1.6", "karma-ie-launcher": "^0.2.0", "karma-mocha": "^0.2.0", "karma-safari-launcher": "^0.1.1", "karma-sauce-launcher": "^0.2.14", "lodash": "^3.10.0", "mocha": "^2.3.3", "node-tick-processor": "~0.0.2", "regenerate": "~1.2.1", "temp": "~0.8.3", "unicode-7.0.0": "~0.1.5"}, "engines": {"node": ">=0.10.0"}, "files": ["bin", "unit-tests.js", "esprima.js"], "homepage": "http://esprima.org", "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "license": "BSD-2-<PERSON><PERSON>", "main": "esprima.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://ariya.ofilabs.com"}], "name": "esprima", "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "scripts": {"all-tests": "npm run generate-fixtures && npm run unit-tests && npm run grammar-tests && npm run regression-tests", "analyze-coverage": "istanbul cover test/unit-tests.js", "appveyor": "npm run all-tests && npm run browser-tests && npm run dynamic-analysis", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick", "browser-tests": "npm run generate-fixtures && cd test && karma start --single-run", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "check-version": "node test/check-version.js", "circleci": "npm test && npm run codecov && npm run downstream", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "complexity": "node test/check-complexity.js", "downstream": "node test/downstream.js", "droneio": "npm test && npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "eslint": "node node_modules/eslint/bin/eslint.js -c .lintrc esprima.js", "generate-fixtures": "node tools/generate-fixtures.js", "generate-regex": "node tools/generate-identifier-regex.js", "grammar-tests": "node test/grammar-tests.js", "jscs": "jscs -p crockford esprima.js && jscs -p crockford test/*.js", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "regression-tests": "node test/regression-tests.js", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "static-analysis": "npm run check-version && npm run jscs && npm run eslint && npm run complexity", "test": "npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "travis": "npm test", "unit-tests": "node test/unit-tests.js"}, "version": "2.7.3"}