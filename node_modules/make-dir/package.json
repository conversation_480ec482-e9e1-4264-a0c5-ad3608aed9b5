{"_from": "make-dir@^1.0.0", "_id": "make-dir@1.3.0", "_inBundle": false, "_integrity": "sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ==", "_location": "/make-dir", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "make-dir@^1.0.0", "name": "make-dir", "escapedName": "make-dir", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/find-cache-dir"], "_resolved": "https://registry.npmjs.org/make-dir/-/make-dir-1.3.0.tgz", "_shasum": "79c1033b80515bd6d24ec9933e860ca75ee27f0c", "_spec": "make-dir@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/find-cache-dir", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "bundleDependencies": false, "dependencies": {"pify": "^3.0.0"}, "deprecated": false, "description": "Make a directory and its parents if needed - Think `mkdir -p`", "devDependencies": {"ava": "*", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^11.3.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "xo": "^0.20.0"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/make-dir#readme", "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "license": "MIT", "name": "make-dir", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "scripts": {"test": "xo && nyc ava"}, "version": "1.3.0"}