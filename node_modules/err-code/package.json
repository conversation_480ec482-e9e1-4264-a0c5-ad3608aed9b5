{"_from": "err-code@^2.0.2", "_id": "err-code@2.0.3", "_inBundle": false, "_integrity": "sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==", "_location": "/err-code", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "err-code@^2.0.2", "name": "err-code", "escapedName": "err-code", "rawSpec": "^2.0.2", "saveSpec": null, "fetchSpec": "^2.0.2"}, "_requiredBy": ["/promise-retry"], "_resolved": "https://registry.npmjs.org/err-code/-/err-code-2.0.3.tgz", "_shasum": "23c2f3b756ffdfc608d30e27c9a941024807e7f9", "_spec": "err-code@^2.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/promise-retry", "author": {"name": "IndigoUnited", "email": "<EMAIL>", "url": "http://indigounited.com"}, "bugs": {"url": "https://github.com/IndigoUnited/js-err-code/issues/"}, "bundleDependencies": false, "deprecated": false, "description": "Create an error with a code", "devDependencies": {"@satazor/eslint-config": "^3.0.0", "browserify": "^16.5.1", "eslint": "^7.2.0", "expect.js": "^0.3.1", "mocha": "^8.0.1"}, "homepage": "https://github.com/IndigoUnited/js-err-code#readme", "keywords": ["error", "err", "code", "properties", "property"], "license": "MIT", "main": "index.js", "name": "err-code", "repository": {"type": "git", "url": "git://github.com/IndigoUnited/js-err-code.git"}, "scripts": {"browserify": "browserify -s err-code index.js > index.umd.js", "lint": "eslint '{*.js,test/**/*.js}' --ignore-pattern *.umd.js", "test": "mocha --bail"}, "version": "2.0.3"}