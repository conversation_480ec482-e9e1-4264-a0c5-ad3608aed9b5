{"_args": [["supports-color@3.2.3", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "supports-color@3.2.3", "_id": "supports-color@3.2.3", "_inBundle": false, "_integrity": "sha512-Jds2VIYDrlp5ui7t8abHN2bjAu4LV/q4N2KivFPpGH0lrka0BMq/33AmECUXlKPcHigkNaqfXRENFju+rlcy+A==", "_location": "/postcss-calc/supports-color", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "supports-color@3.2.3", "name": "supports-color", "escapedName": "supports-color", "rawSpec": "3.2.3", "saveSpec": null, "fetchSpec": "3.2.3"}, "_requiredBy": ["/postcss-calc/postcss"], "_resolved": "https://registry.npmjs.org/supports-color/-/supports-color-3.2.3.tgz", "_spec": "3.2.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "browser": "browser.js", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dependencies": {"has-flag": "^1.0.0"}, "description": "Detect whether a terminal supports color", "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2", "xo": "*"}, "engines": {"node": ">=0.8.0"}, "files": ["index.js", "browser.js"], "homepage": "https://github.com/chalk/supports-color#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m", "million"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbna.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/qix-"}], "name": "supports-color", "repository": {"type": "git", "url": "git+https://github.com/chalk/supports-color.git"}, "scripts": {"test": "xo && mocha", "travis": "mocha"}, "version": "3.2.3", "xo": {"envs": ["node", "mocha"]}}