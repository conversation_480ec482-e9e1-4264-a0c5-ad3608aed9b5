{"_from": "hash-sum@^1.0.2", "_id": "hash-sum@1.0.2", "_inBundle": false, "_integrity": "sha512-fUs4B4L+mlt8/XAtSOGMUO1TXmAelItBPtJG7CyHJfYTdDjwisntGO2JQz7oUsatOY9o68+57eziUVNw/mRHmA==", "_location": "/hash-sum", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "hash-sum@^1.0.2", "name": "hash-sum", "escapedName": "hash-sum", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/vue-loader", "/vue-style-loader"], "_resolved": "https://registry.npmjs.org/hash-sum/-/hash-sum-1.0.2.tgz", "_shasum": "33b40777754c6432573c120cc3808bbd10d47f04", "_spec": "hash-sum@^1.0.2", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/vue-loader", "authors": ["<PERSON> <nicolas<PERSON><PERSON><PERSON><PERSON>@gmail.com>"], "bugs": {"url": "https://github.com/bevacqua/hash-sum/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Blazing fast unique hash generator", "devDependencies": {"jshint": "2.5.0", "jshint-stylish": "0.2.0", "tape": "3.0.3"}, "homepage": "https://github.com/bevacqua/hash-sum", "license": "MIT", "main": "hash-sum.js", "name": "hash-sum", "repository": {"type": "git", "url": "git://github.com/bevacqua/hash-sum.git"}, "scripts": {"test": "jshint . && tape test.js"}, "version": "1.0.2"}