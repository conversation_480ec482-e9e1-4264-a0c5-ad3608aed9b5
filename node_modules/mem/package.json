{"_from": "mem@^1.1.0", "_id": "mem@1.1.0", "_inBundle": false, "_integrity": "sha512-nOBDrc/wgpkd3X/JOhMqYR+/eLqlfLP4oQfoBA6QExIxEl+GU01oyEkwWyueyO8110pUKijtiHGhEmYoOn88oQ==", "_location": "/mem", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mem@^1.1.0", "name": "mem", "escapedName": "mem", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/os-locale"], "_resolved": "https://registry.npmjs.org/mem/-/mem-1.1.0.tgz", "_shasum": "5edd52b485ca1d900fe64895505399a0dfa45f76", "_spec": "mem@^1.1.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/os-locale", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/mem/issues"}, "bundleDependencies": false, "dependencies": {"mimic-fn": "^1.0.0"}, "deprecated": false, "description": "Memoize functions - An optimization used to speed up consecutive function calls by caching the result of calls with identical input", "devDependencies": {"ava": "*", "delay": "^1.1.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/mem#readme", "keywords": ["memoize", "function", "mem", "memoization", "cache", "caching", "optimize", "performance", "ttl", "expire", "promise"], "license": "MIT", "name": "mem", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mem.git"}, "scripts": {"test": "xo && ava"}, "version": "1.1.0", "xo": {"esnext": true}}