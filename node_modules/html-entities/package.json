{"_from": "html-entities@^1.2.0", "_id": "html-entities@1.4.0", "_inBundle": false, "_integrity": "sha512-8nxjcBcd8wovbeKx7h3wTji4e6+rhaVuPNpMqwWgnHh+N9ToqsCs6XztWRBPQ+UtzsoMAdKZtUENoVzU/EMtZA==", "_location": "/html-entities", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "html-entities@^1.2.0", "name": "html-entities", "escapedName": "html-entities", "rawSpec": "^1.2.0", "saveSpec": null, "fetchSpec": "^1.2.0"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://registry.npmjs.org/html-entities/-/html-entities-1.4.0.tgz", "_shasum": "cfbd1b01d2afaf9adca1b10ae7dffab98c71d2dc", "_spec": "html-entities@^1.2.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/webpack-dev-server", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mdevils/node-html-entities/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Faster HTML entities encode/decode library.", "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "benchmark": "^2.1.4", "chai": "^4.2.0", "coveralls": "^3.1.0", "entities": "^2.0.0", "mocha": "^7.1.2", "node-html-encoder": "^0.0.2", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "files": ["index.js", "lib", "LICENSE"], "homepage": "https://github.com/mdevils/node-html-entities#readme", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "license": "MIT", "main": "./lib/index.js", "name": "html-entities", "repository": {"type": "git", "url": "git+https://github.com/mdevils/node-html-entities.git"}, "scripts": {"benchmark": "ts-node benchmark/benchmark", "build": "tsc", "prepublishOnly": "yarn build", "test": "mocha --recursive -r ts-node/register test/**/*.ts", "travis": "yarn test"}, "types": "./lib/index.d.ts", "typings": "./lib/index.d.ts", "version": "1.4.0"}