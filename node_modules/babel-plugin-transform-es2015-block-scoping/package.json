{"_args": [["babel-plugin-transform-es2015-block-scoping@6.26.0", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "babel-plugin-transform-es2015-block-scoping@6.26.0", "_id": "babel-plugin-transform-es2015-block-scoping@6.26.0", "_inBundle": false, "_integrity": "sha512-YiN6sFAQ5lML8JjCmr7uerS5Yc/EMbgg9G8ZNmk2E3nYX4ckHR01wrkeeMijEf5WHNK5TW0Sl0Uu3pv3EdOJWw==", "_location": "/babel-plugin-transform-es2015-block-scoping", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "babel-plugin-transform-es2015-block-scoping@6.26.0", "name": "babel-plugin-transform-es2015-block-scoping", "escapedName": "babel-plugin-transform-es2015-block-scoping", "rawSpec": "6.26.0", "saveSpec": null, "fetchSpec": "6.26.0"}, "_requiredBy": ["/babel-preset-env"], "_resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-block-scoping/-/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz", "_spec": "6.26.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "dependencies": {"babel-runtime": "^6.26.0", "babel-template": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "lodash": "^4.17.4"}, "description": "Compile ES2015 block scoping (const and let) to ES5", "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}, "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "babel-plugin-transform-es2015-block-scoping", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-es2015-block-scoping"}, "version": "6.26.0"}