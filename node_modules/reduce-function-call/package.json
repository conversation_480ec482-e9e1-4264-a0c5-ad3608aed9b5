{"_args": [["reduce-function-call@1.0.3", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "reduce-function-call@1.0.3", "_id": "reduce-function-call@1.0.3", "_inBundle": false, "_integrity": "sha512-Hl/tuV2VDgWgCSEeWMLwxLZqX7OK59eU1guxXsRKTAyeYimivsKdtcV4fu3r710tpG5GmDKDhQ0HSZLExnNmyQ==", "_location": "/reduce-function-call", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "reduce-function-call@1.0.3", "name": "reduce-function-call", "escapedName": "reduce-function-call", "rawSpec": "1.0.3", "saveSpec": null, "fetchSpec": "1.0.3"}, "_requiredBy": ["/reduce-css-calc"], "_resolved": "https://registry.npmjs.org/reduce-function-call/-/reduce-function-call-1.0.3.tgz", "_spec": "1.0.3", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "MoOx"}, "bugs": {"url": "https://github.com/MoOx/reduce-function-call/issues"}, "dependencies": {"balanced-match": "^1.0.0"}, "description": "Reduce function calls in a string, using a callback", "devDependencies": {"npmpub": "^4.1.0", "tape": "^4.0.3"}, "files": ["CHANGELOG.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/MoOx/reduce-function-call#readme", "keywords": ["string", "reduce", "replacement", "function", "call", "eval", "interpret"], "license": "MIT", "name": "reduce-function-call", "repository": {"type": "git", "url": "git+https://github.com/MoOx/reduce-function-call.git"}, "scripts": {"release": "npmpub", "test": "tape test"}, "version": "1.0.3"}