{"_args": [["create-hmac@1.1.7", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "create-hmac@1.1.7", "_id": "create-hmac@1.1.7", "_inBundle": false, "_integrity": "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==", "_location": "/create-hmac", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "create-hmac@1.1.7", "name": "create-hmac", "escapedName": "create-hmac", "rawSpec": "1.1.7", "saveSpec": null, "fetchSpec": "1.1.7"}, "_requiredBy": ["/browserify-sign", "/crypto-browserify", "/pbkdf2"], "_resolved": "https://registry.npmjs.org/create-hmac/-/create-hmac-1.1.7.tgz", "_spec": "1.1.7", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": "", "browser": "./browser.js", "bugs": {"url": "https://github.com/crypto-browserify/createHmac/issues"}, "dependencies": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}, "description": "node style hmacs in the browser", "devDependencies": {"hash-test-vectors": "^1.3.2", "standard": "^5.3.1", "tap-spec": "^2.1.2", "tape": "^3.0.3"}, "files": ["browser.js", "index.js", "legacy.js"], "homepage": "https://github.com/crypto-browserify/createHmac", "keywords": ["crypto", "hmac"], "license": "MIT", "main": "index.js", "name": "create-hmac", "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/createHmac.git"}, "scripts": {"standard": "standard", "test": "npm run-script standard && npm run-script unit", "unit": "node test.js | tspec"}, "version": "1.1.7"}