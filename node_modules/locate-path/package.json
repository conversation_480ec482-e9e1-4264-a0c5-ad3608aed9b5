{"_from": "locate-path@^5.0.0", "_id": "locate-path@5.0.0", "_inBundle": false, "_integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "_location": "/locate-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "locate-path@^5.0.0", "name": "locate-path", "escapedName": "locate-path", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["/find-up"], "_resolved": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "_shasum": "1afba396afd676a6d42504d0a67a3a7eb9f62aa0", "_spec": "locate-path@^5.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/find-up", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "bundleDependencies": false, "dependencies": {"p-locate": "^4.1.0"}, "deprecated": false, "description": "Get the first path that exists on disk of multiple paths", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/locate-path#readme", "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "license": "MIT", "name": "locate-path", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "5.0.0"}