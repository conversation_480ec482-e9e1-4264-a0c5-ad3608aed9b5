{"_from": "is-svg@^2.0.0", "_id": "is-svg@2.1.0", "_inBundle": false, "_integrity": "sha512-Ya1giYJUkcL/94quj0+XGcmts6cETPBW1MiFz1ReJrnDJ680F52qpAEGAEGU0nq96FRGIGPx6Yo1CyPXcOoyGw==", "_location": "/is-svg", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-svg@^2.0.0", "name": "is-svg", "escapedName": "is-svg", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/postcss-svgo"], "_resolved": "https://registry.npmjs.org/is-svg/-/is-svg-2.1.0.tgz", "_shasum": "cf61090da0d9efbcab8722deba6f032208dbb0e9", "_spec": "is-svg@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/postcss-svgo", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-svg/issues"}, "bundleDependencies": false, "dependencies": {"html-comment-regex": "^1.1.0"}, "deprecated": false, "description": "Check if a string or buffer is SVG", "devDependencies": {"ava": "*", "xo": "^0.16.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/is-svg#readme", "keywords": ["svg", "vector", "graphics", "image", "img", "pic", "picture", "type", "detect", "check", "is", "string", "str", "buffer"], "license": "MIT", "name": "is-svg", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-svg.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.0"}