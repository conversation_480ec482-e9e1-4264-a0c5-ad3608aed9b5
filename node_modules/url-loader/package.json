{"_args": [["url-loader@0.5.9", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "url-loader@0.5.9", "_id": "url-loader@0.5.9", "_inBundle": false, "_integrity": "sha512-B7QYFyvv+fOBqBVeefsxv6koWWtjmHaMFT6KZWti4KRw8YUD/hOU+3AECvXuzyVawIBx3z7zQRejXCDSO5kk1Q==", "_location": "/url-loader", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "url-loader@0.5.9", "name": "url-loader", "escapedName": "url-loader", "rawSpec": "0.5.9", "saveSpec": null, "fetchSpec": "0.5.9"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/url-loader/-/url-loader-0.5.9.tgz", "_spec": "0.5.9", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack/url-loader/issues"}, "dependencies": {"loader-utils": "^1.0.2", "mime": "1.3.x"}, "description": "url loader module for webpack", "devDependencies": {"standard-version": "^4.0.0"}, "homepage": "https://github.com/webpack/url-loader#readme", "license": "MIT", "name": "url-loader", "peerDependencies": {"file-loader": "*"}, "repository": {"type": "git", "url": "git+ssh://**************/webpack/url-loader.git"}, "scripts": {"release": "standard-version"}, "version": "0.5.9"}