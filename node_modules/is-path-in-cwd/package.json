{"_from": "is-path-in-cwd@^1.0.0", "_id": "is-path-in-cwd@1.0.1", "_inBundle": false, "_integrity": "sha512-FjV1RTW48E7CWM7eE/J2NJvAEEVektecDBVBE5Hh3nM1Jd0kvhHtX68Pr3xsDf857xt3Y4AkwVULK1Vku62aaQ==", "_location": "/is-path-in-cwd", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-path-in-cwd@^1.0.0", "name": "is-path-in-cwd", "escapedName": "is-path-in-cwd", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/del"], "_resolved": "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-1.0.1.tgz", "_shasum": "5ac48b345ef675339bd6c7a48a912110b241cf52", "_spec": "is-path-in-cwd@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/del", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-path-in-cwd/issues"}, "bundleDependencies": false, "dependencies": {"is-path-inside": "^1.0.0"}, "deprecated": false, "description": "Check if a path is in the current working directory", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/is-path-in-cwd#readme", "keywords": ["path", "cwd", "pwd", "check", "filepath", "file", "folder", "in", "inside"], "license": "MIT", "name": "is-path-in-cwd", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-path-in-cwd.git"}, "scripts": {"test": "mocha"}, "version": "1.0.1"}