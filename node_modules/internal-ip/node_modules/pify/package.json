{"_from": "pify@^2.0.0", "_id": "pify@2.3.0", "_inBundle": false, "_integrity": "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==", "_location": "/internal-ip/pify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pify@^2.0.0", "name": "pify", "escapedName": "pify", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/internal-ip/load-json-file", "/internal-ip/path-type"], "_resolved": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "_shasum": "ed141a6ac043a849ea588498e7dca8b15330e90c", "_spec": "pify@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/load-json-file", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/pify/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Promisify a callback-style function", "devDependencies": {"ava": "*", "pinkie-promise": "^1.0.0", "v8-natives": "0.0.2", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/pify#readme", "keywords": ["promise", "promises", "promisify", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "es2015"], "license": "MIT", "name": "pify", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/pify.git"}, "scripts": {"optimization-test": "node --allow-natives-syntax optimization-test.js", "test": "xo && ava && npm run optimization-test"}, "version": "2.3.0"}