{"_from": "strip-bom@^2.0.0", "_id": "strip-bom@2.0.0", "_inBundle": false, "_integrity": "sha512-kwrX1y7czp1E69n2ajbG65mIo9dqvJ+8aBQXOGVxqwvNbsXdFM6Lq37dLAY3mknUwru8CfcCbfOLL/gMo+fi3g==", "_location": "/internal-ip/strip-bom", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "strip-bom@^2.0.0", "name": "strip-bom", "escapedName": "strip-bom", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/internal-ip/load-json-file"], "_resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz", "_shasum": "6219a85616520491f35788bdbf1447a99c7e6b0e", "_spec": "strip-bom@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/load-json-file", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/strip-bom/issues"}, "bundleDependencies": false, "dependencies": {"is-utf8": "^0.2.0"}, "deprecated": false, "description": "Strip UTF-8 byte order mark (BOM) from a string/buffer", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/strip-bom#readme", "keywords": ["bom", "strip", "byte", "mark", "unicode", "utf8", "utf-8", "remove", "delete", "trim", "text", "buffer", "string"], "license": "MIT", "name": "strip-bom", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-bom.git"}, "scripts": {"test": "mocha"}, "version": "2.0.0"}