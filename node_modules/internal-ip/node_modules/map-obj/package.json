{"_from": "map-obj@^1.0.1", "_id": "map-obj@1.0.1", "_inBundle": false, "_integrity": "sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg==", "_location": "/internal-ip/map-obj", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "map-obj@^1.0.1", "name": "map-obj", "escapedName": "map-obj", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/internal-ip/camelcase-keys", "/internal-ip/meow"], "_resolved": "https://registry.npmjs.org/map-obj/-/map-obj-1.0.1.tgz", "_shasum": "d933ceb9205d82bdcf4886f6742bdc2b4dea146d", "_spec": "map-obj@^1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/meow", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/map-obj/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Map object keys and values into a new object", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/map-obj#readme", "keywords": ["map", "obj", "object", "key", "keys", "value", "values", "val", "iterate", "iterator"], "license": "MIT", "name": "map-obj", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/map-obj.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.1"}