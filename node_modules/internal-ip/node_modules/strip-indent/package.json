{"_from": "strip-indent@^1.0.1", "_id": "strip-indent@1.0.1", "_inBundle": false, "_integrity": "sha512-I5iQq6aFMM62fBEAIB/hXzwJD6EEZ0xEGCX2t7oXqaKPIRgt4WruAQ285BISgdkP+HLGWyeGmNJcpIwFeRYRUA==", "_location": "/internal-ip/strip-indent", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "strip-indent@^1.0.1", "name": "strip-indent", "escapedName": "strip-indent", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/internal-ip/redent"], "_resolved": "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.1.tgz", "_shasum": "0c7962a6adefa7bbd4ac366460a638552ae1a0a2", "_spec": "strip-indent@^1.0.1", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/redent", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"strip-indent": "cli.js"}, "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "bundleDependencies": false, "dependencies": {"get-stdin": "^4.0.1"}, "deprecated": false, "description": "Strip leading whitespace from every line in a string", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "cli.js"], "homepage": "https://github.com/sindresorhus/strip-indent#readme", "keywords": ["cli", "bin", "browser", "strip", "normalize", "remove", "indent", "indentation", "whitespace", "space", "tab", "string", "str"], "license": "MIT", "name": "strip-indent", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-indent.git"}, "scripts": {"test": "mocha"}, "version": "1.0.1"}