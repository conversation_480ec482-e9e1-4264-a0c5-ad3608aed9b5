{"_from": "trim-newlines@^1.0.0", "_id": "trim-newlines@1.0.0", "_inBundle": false, "_integrity": "sha512-Nm4cF79FhSTzrLKGDMi3I4utBtFv8qKy4sq1enftf2gMdpqI8oVQTAfySkTz5r49giVzDj88SVZXP4CeYQwjaw==", "_location": "/internal-ip/trim-newlines", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "trim-newlines@^1.0.0", "name": "trim-newlines", "escapedName": "trim-newlines", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/internal-ip/meow"], "_resolved": "https://registry.npmjs.org/trim-newlines/-/trim-newlines-1.0.0.tgz", "_shasum": "5887966bb582a4503a41eb524f7d35011815a613", "_spec": "trim-newlines@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/meow", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/trim-newlines/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Trim newlines from the start and/or end of a string", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/trim-newlines#readme", "keywords": ["trim", "newline", "newlines", "linebreak", "lf", "crlf", "left", "right", "start", "end", "string", "str", "remove", "delete", "strip"], "license": "MIT", "name": "trim-newlines", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/trim-newlines.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.0"}