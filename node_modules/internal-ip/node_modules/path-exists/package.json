{"_from": "path-exists@^2.0.0", "_id": "path-exists@2.1.0", "_inBundle": false, "_integrity": "sha512-yTltuKuhtNeFJKa1PiRzfLAU5182q1y4Eb4XCJ3PBqyzEDkAZRzBrKKBct682ls9reBVHf9udYLN5Nd+K1B9BQ==", "_location": "/internal-ip/path-exists", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "path-exists@^2.0.0", "name": "path-exists", "escapedName": "path-exists", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/internal-ip/find-up"], "_resolved": "https://registry.npmjs.org/path-exists/-/path-exists-2.1.0.tgz", "_shasum": "0feb6c64f0fc518d9a754dd5efb62c7022761f4b", "_spec": "path-exists@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/find-up", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/path-exists/issues"}, "bundleDependencies": false, "dependencies": {"pinkie-promise": "^2.0.0"}, "deprecated": false, "description": "Check if a path exists", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/path-exists#readme", "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "license": "MIT", "name": "path-exists", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-exists.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.0"}