{"_from": "load-json-file@^1.0.0", "_id": "load-json-file@1.1.0", "_inBundle": false, "_integrity": "sha512-cy7ZdNRXdablkXYNI049pthVeXFurRyb9+hA/dZzerZ0pGTx42z+y+ssxBaVV2l70t1muq5IdKhn4UtcoGUY9A==", "_location": "/internal-ip/load-json-file", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "load-json-file@^1.0.0", "name": "load-json-file", "escapedName": "load-json-file", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/internal-ip/read-pkg"], "_resolved": "https://registry.npmjs.org/load-json-file/-/load-json-file-1.1.0.tgz", "_shasum": "956905708d58b4bab4c2261b04f59f31c99374c0", "_spec": "load-json-file@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/read-pkg", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/load-json-file/issues"}, "bundleDependencies": false, "dependencies": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}, "deprecated": false, "description": "Read and parse a JSON file", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/load-json-file#readme", "keywords": ["json", "read", "parse", "file", "fs", "graceful", "load"], "license": "MIT", "name": "load-json-file", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/load-json-file.git"}, "scripts": {"test": "xo && ava"}, "version": "1.1.0", "xo": {"ignores": ["test.js"]}}