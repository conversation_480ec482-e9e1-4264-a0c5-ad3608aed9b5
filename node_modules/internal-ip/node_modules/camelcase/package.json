{"_from": "camelcase@^2.0.0", "_id": "camelcase@2.1.1", "_inBundle": false, "_integrity": "sha512-DLIsRzJVBQu72meAKPkWQOLcujdXT32hwdfnkI1frSiSRMK1MofjKHf+MEx0SB6fjEFXL8fBDv1dKymBlOp4Qw==", "_location": "/internal-ip/camelcase", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "camelcase@^2.0.0", "name": "camelcase", "escapedName": "camelcase", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/internal-ip/camelcase-keys"], "_resolved": "https://registry.npmjs.org/camelcase/-/camelcase-2.1.1.tgz", "_shasum": "7c1d16d679a1bbe59ca02cacecfb011e201f5a1f", "_spec": "camelcase@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/camelcase-keys", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/camelcase#readme", "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "license": "MIT", "name": "camelcase", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.1"}