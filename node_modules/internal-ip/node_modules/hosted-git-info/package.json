{"_from": "hosted-git-info@^2.1.4", "_id": "hosted-git-info@2.8.9", "_inBundle": false, "_integrity": "sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==", "_location": "/internal-ip/hosted-git-info", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "hosted-git-info@^2.1.4", "name": "hosted-git-info", "escapedName": "hosted-git-info", "rawSpec": "^2.1.4", "saveSpec": null, "fetchSpec": "^2.1.4"}, "_requiredBy": ["/internal-ip/normalize-package-data"], "_resolved": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.9.tgz", "_shasum": "dffc0bf9a21c02209090f2aa69429e1414daf3f9", "_spec": "hosted-git-info@^2.1.4", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/normalize-package-data", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org"}, "bugs": {"url": "https://github.com/npm/hosted-git-info/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Provides metadata and conversions from repository urls for Github, Bitbucket and Gitlab", "devDependencies": {"standard": "^11.0.1", "standard-version": "^4.4.0", "tap": "^12.7.0"}, "files": ["index.js", "git-host.js", "git-host-info.js"], "homepage": "https://github.com/npm/hosted-git-info", "keywords": ["git", "github", "bitbucket", "gitlab"], "license": "ISC", "main": "index.js", "name": "hosted-git-info", "repository": {"type": "git", "url": "git+https://github.com/npm/hosted-git-info.git"}, "scripts": {"postrelease": "npm publish --tag=ancient-legacy-fixes && git push --follow-tags", "posttest": "standard", "prerelease": "npm t", "release": "standard-version -s", "test": "tap -J --coverage=90 --no-esm test/*.js", "test:coverage": "tap --coverage-report=html -J --coverage=90 --no-esm test/*.js"}, "version": "2.8.9"}