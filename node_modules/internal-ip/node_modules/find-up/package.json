{"_from": "find-up@^1.0.0", "_id": "find-up@1.1.2", "_inBundle": false, "_integrity": "sha512-jvElSjyuo4EMQGoTwo1uJU5pQMwTW5lS1x05zzfJuTIyLR3zwO27LYrxNg+dlvKpGOuGy/MzBdXh80g0ve5+HA==", "_location": "/internal-ip/find-up", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "find-up@^1.0.0", "name": "find-up", "escapedName": "find-up", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/internal-ip/read-pkg-up"], "_resolved": "https://registry.npmjs.org/find-up/-/find-up-1.1.2.tgz", "_shasum": "6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f", "_spec": "find-up@^1.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/read-pkg-up", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "bundleDependencies": false, "dependencies": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}, "deprecated": false, "description": "Find a file by walking up parent directories", "devDependencies": {"ava": "*", "tempfile": "^1.1.1", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/find-up#readme", "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "license": "MIT", "name": "find-up", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "scripts": {"test": "xo && ava"}, "version": "1.1.2"}