{"_from": "camelcase-keys@^2.0.0", "_id": "camelcase-keys@2.1.0", "_inBundle": false, "_integrity": "sha512-bA/Z/DERHKqoEOrp+qeGKw1QlvEQkGZSc0XaY6VnTxZr+Kv1G5zFwttpjv8qxZ/sBPT4nthwZaAcsAZTJlSKXQ==", "_location": "/internal-ip/camelcase-keys", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "camelcase-keys@^2.0.0", "name": "camelcase-keys", "escapedName": "camelcase-keys", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/internal-ip/meow"], "_resolved": "https://registry.npmjs.org/camelcase-keys/-/camelcase-keys-2.1.0.tgz", "_shasum": "308beeaffdf28119051efa1d932213c91b8f92e7", "_spec": "camelcase-keys@^2.0.0", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue/node_modules/internal-ip/node_modules/meow", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/camelcase-keys/issues"}, "bundleDependencies": false, "dependencies": {"camelcase": "^2.0.0", "map-obj": "^1.0.0"}, "deprecated": false, "description": "Convert object keys to camelCase", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/camelcase-keys#readme", "keywords": ["map", "obj", "object", "key", "keys", "value", "values", "val", "iterate", "camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "license": "MIT", "name": "camelcase-keys", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase-keys.git"}, "scripts": {"test": "xo && ava"}, "version": "2.1.0"}