{"_args": [["sha.js@2.4.12", "/Users/<USER>/tmp/1/we-chat-app-admin-vue"]], "_development": true, "_from": "sha.js@2.4.12", "_id": "sha.js@2.4.12", "_inBundle": false, "_integrity": "sha512-8LzC5+bvI45BjpfXU8V5fdU2mfeKiQe1D1gIMn7XUlF3OTUrpdJpPPH4EMAnF0DsHHdSZqCdSss5qCmJKuiO3w==", "_location": "/sha.js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "sha.js@2.4.12", "name": "sha.js", "escapedName": "sha.js", "rawSpec": "2.4.12", "saveSpec": null, "fetchSpec": "2.4.12"}, "_requiredBy": ["/create-hash", "/create-hmac", "/pbkdf2", "/pbkdf2/create-hash"], "_resolved": "https://registry.npmjs.org/sha.js/-/sha.js-2.4.12.tgz", "_spec": "2.4.12", "_where": "/Users/<USER>/tmp/1/we-chat-app-admin-vue", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bin": {"sha.js": "bin.js"}, "bugs": {"url": "https://github.com/crypto-browserify/sha.js/issues"}, "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1", "to-buffer": "^1.2.0"}, "description": "Streamable SHA hashes in pure javascript", "devDependencies": {"@ljharb/eslint-config": "^21.2.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "hash-test-vectors": "^1.3.2", "npmignore": "^0.3.1", "tape": "^5.9.0", "typedarray": "^0.0.7"}, "engines": {"node": ">= 0.10"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/crypto-browserify/sha.js", "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "name": "sha.js", "publishConfig": {"ignore": [".github/workflows", ".github"]}, "repository": {"type": "git", "url": "git://github.com/crypto-browserify/sha.js.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "2.4.12"}